<?php
/**
 * Birthday Gift Dashboard Widget
 * 
 * Widget for the admin dashboard showing birthday gift activity and opportunities
 */

require_once '../includes/BirthdayGiftCoordinator.php';

// Initialize coordinator
$coordinator = new BirthdayGiftCoordinator($pdo);
$dashboard_data = $coordinator->getBirthdayGiftDashboard();

?>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-gift-fill text-warning"></i> Birthday Gifts
                </h5>
                <div>
                    <a href="gift_management.php" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-gear"></i> Manage
                    </a>
                    <a href="donation_management.php" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle"></i> Send Gift
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Statistics Row -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary mb-1"><?php echo number_format($dashboard_data['stats']['total_gifts_this_month'] ?? 0); ?></h4>
                            <small class="text-muted">Gifts This Month</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success mb-1">$<?php echo number_format($dashboard_data['stats']['total_amount_this_month'] ?? 0, 2); ?></h4>
                            <small class="text-muted">Total Amount</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info mb-1"><?php echo number_format($dashboard_data['stats']['delivered_this_month'] ?? 0); ?></h4>
                            <small class="text-muted">Delivered</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning mb-1"><?php echo number_format($dashboard_data['stats']['pending_this_month'] ?? 0); ?></h4>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Today's Birthdays -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">
                            <i class="bi bi-calendar-event"></i> Today's Birthdays
                        </h6>
                        <?php if (empty($dashboard_data['todays_birthdays'])): ?>
                            <div class="text-center py-3">
                                <i class="bi bi-calendar-x text-muted" style="font-size: 2rem;"></i>
                                <p class="text-muted mb-0">No birthdays today</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($dashboard_data['todays_birthdays'] as $birthday): ?>
                                    <div class="list-group-item px-0">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">
                                                    <i class="bi bi-person-fill text-primary"></i>
                                                    <?php echo htmlspecialchars($birthday['full_name']); ?>
                                                </h6>
                                                <?php if ($birthday['gifts_received_today'] > 0): ?>
                                                    <small class="text-success">
                                                        <i class="bi bi-gift"></i>
                                                        <?php echo $birthday['gifts_received_today'] ?? 0; ?> gift(s) received
                                                        ($<?php echo number_format($birthday['total_gift_amount_today'] ?? 0, 2); ?>)
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">No gifts received yet</small>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <a href="../donate.php?recipient=<?php echo $birthday['id']; ?>"
                                                   class="btn btn-sm btn-outline-warning"
                                                   target="_blank"
                                                   title="Send Birthday Gift">
                                                    <i class="bi bi-gift"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Upcoming Birthdays -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">
                            <i class="bi bi-calendar-plus"></i> Upcoming Birthdays (7 days)
                        </h6>
                        <?php if (empty($dashboard_data['upcoming_birthdays'])): ?>
                            <div class="text-center py-3">
                                <i class="bi bi-calendar-check text-muted" style="font-size: 2rem;"></i>
                                <p class="text-muted mb-0">No upcoming birthdays</p>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php 
                                $shown = 0;
                                foreach ($dashboard_data['upcoming_birthdays'] as $birthday): 
                                    if ($birthday['days_until_birthday'] > 0 && $shown < 5):
                                        $shown++;
                                ?>
                                    <div class="list-group-item px-0">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">
                                                    <i class="bi bi-person text-info"></i>
                                                    <?php echo htmlspecialchars($birthday['full_name']); ?>
                                                </h6>
                                                <small class="text-muted">
                                                    <?php 
                                                    $days = $birthday['days_until_birthday'];
                                                    echo $days == 1 ? 'Tomorrow' : "In $days days";
                                                    ?>
                                                    <?php if ($birthday['gift_count'] > 0): ?>
                                                        • <?php echo $birthday['gift_count']; ?> gift(s) this year
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div>
                                                <span class="badge bg-light text-dark">
                                                    <?php echo date('M j', strtotime($birthday['birth_date'])); ?>
                                                </span>
                                                <a href="../donate.php?recipient=<?php echo $birthday['id']; ?>"
                                                   class="btn btn-sm btn-outline-primary ms-1"
                                                   target="_blank"
                                                   title="Send Birthday Gift">
                                                    <i class="bi bi-gift"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                                <?php if (count($dashboard_data['upcoming_birthdays']) > 5): ?>
                                    <div class="list-group-item px-0 text-center">
                                        <a href="birthday_calendar.php" class="text-muted">
                                            <small>View all upcoming birthdays...</small>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Gift Activity -->
                <?php if (!empty($dashboard_data['recent_gifts'])): ?>
                    <hr class="my-4">
                    <h6 class="text-muted mb-3">
                        <i class="bi bi-clock-history"></i> Recent Gift Activity
                    </h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Recipient</th>
                                    <th>Amount</th>
                                    <th>Donor</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($dashboard_data['recent_gifts'], 0, 5) as $gift): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($gift['recipient_name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php echo $gift['currency'] ?? '$'; ?> <?php echo number_format($gift['amount'] ?? 0, 2); ?>
                                        </td>
                                        <td>
                                            <?php if ($gift['anonymous_gift']): ?>
                                                <em class="text-muted">Anonymous</em>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($gift['donor_name']); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('M j, g:i A', strtotime($gift['created_at'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'pending' => 'bg-warning',
                                                'scheduled' => 'bg-info',
                                                'delivered' => 'bg-success',
                                                'failed' => 'bg-danger'
                                            ];
                                            $status_class = $status_classes[$gift['delivery_status']] ?? 'bg-secondary';
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>" style="font-size: 0.7rem;">
                                                <?php echo ucfirst($gift['delivery_status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="gift_management.php" class="btn btn-sm btn-outline-secondary">
                            View All Gifts <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.list-group-item {
    border-left: none;
    border-right: none;
}
.list-group-item:first-child {
    border-top: none;
}
.list-group-item:last-child {
    border-bottom: none;
}
</style>
