<?php
/**
 * Frontend CSS Loader
 * Loads custom theme CSS from admin appearance settings
 * Include this in frontend pages after Bootstrap CSS
 */

// Include config to access settings functions
if (!function_exists('get_site_setting')) {
    require_once __DIR__ . '/../config.php';
}

// Load custom theme CSS if it exists
$customCssPath = __DIR__ . '/../admin/css/custom-theme.css';
$customCssUrl = 'admin/css/custom-theme.css';

if (file_exists($customCssPath)) {
    $cssVersion = filemtime($customCssPath);
    echo '<link rel="stylesheet" href="' . $customCssUrl . '?v=' . $cssVersion . '">' . "\n";
} else {
    // Fallback: generate CSS inline if no files exist
    $primary_color = get_site_setting('primary_color', '#007bff');
    $secondary_color = get_site_setting('secondary_color', '#6c757d');
    $success_color = get_site_setting('success_color', '#28a745');
    $danger_color = get_site_setting('danger_color', '#dc3545');
    $warning_color = get_site_setting('warning_color', '#ffc107');
    $info_color = get_site_setting('info_color', '#17a2b8');
    $light_color = get_site_setting('light_color', '#f8f9fa');
    $dark_color = get_site_setting('dark_color', '#343a40');
    $font_family = get_site_setting('primary_font', 'Inter');
    $font_size = get_site_setting('font_size_base', '16');
    $line_height = get_site_setting('line_height_base', '1.5');
    $border_radius = get_site_setting('border_radius', '0.375');

    echo '<style>';
    echo ':root {';
    echo '--bs-primary: ' . $primary_color . ';';
    echo '--bs-secondary: ' . $secondary_color . ';';
    echo '--bs-success: ' . $success_color . ';';
    echo '--bs-danger: ' . $danger_color . ';';
    echo '--bs-warning: ' . $warning_color . ';';
    echo '--bs-info: ' . $info_color . ';';
    echo '--bs-light: ' . $light_color . ';';
    echo '--bs-dark: ' . $dark_color . ';';
    echo '--bs-font-sans-serif: \'' . $font_family . '\', system-ui, -apple-system, sans-serif;';
    echo '--bs-body-font-size: ' . $font_size . 'px;';
    echo '--bs-body-line-height: ' . $line_height . ';';
    echo '--bs-border-radius: ' . $border_radius . 'rem;';
    echo '}';
    echo '</style>' . "\n";
}

// Load favicon if available
$favicon_path = get_site_setting('favicon_path', '');
if ($favicon_path && file_exists(__DIR__ . '/../' . $favicon_path)): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo $favicon_path; ?>">
<?php endif; ?>
