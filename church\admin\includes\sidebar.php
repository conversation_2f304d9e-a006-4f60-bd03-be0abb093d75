<?php
// Include language system
require_once __DIR__ . '/language.php';

// Get the current page filename to highlight the active menu item
$current_page = basename($_SERVER['PHP_SELF']);

// Helper function to build admin URL consistently
if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        if (defined('ADMIN_URL')) {
            return ADMIN_URL . '/' . $page;
        }
        return $page; // Fallback for pages in the same directory
    }
}

// Helper function to check if current page is active
if (!function_exists('is_active')) {
    function is_active($page_name) {
        global $current_page;
        return ($current_page == $page_name) ? 'class="active"' : '';
    }
}

// Helper function to check if any page in array is active (for collapsible sections)
if (!function_exists('is_section_active')) {
    function is_section_active($pages) {
        global $current_page;
        return in_array($current_page, $pages) ? 'show' : '';
    }
}
?>

<!-- Sidebar -->
<div class="col-auto sidebar themed-sidebar" id="sidebar">
    <div class="sidebar-header position-relative">
        <?php
        // Use the existing logo management system
        $headerLogo = get_site_setting('header_logo', '');
        $mainLogo = get_site_setting('main_logo', '');
        $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

        // Get organization name for initials
        $organizationName = get_organization_name();
        $siteInitials = '';
        if (!empty($organizationName)) {
            $words = explode(' ', $organizationName);
            foreach ($words as $word) {
                if (!empty($word)) {
                    $siteInitials .= strtoupper(substr($word, 0, 1));
                }
            }
            // Limit to 3 characters max
            $siteInitials = substr($siteInitials, 0, 3);
        }
        if (empty($siteInitials)) {
            $siteInitials = 'CA'; // Default fallback
        }
        ?>

        <!-- Logo/Brand for expanded sidebar -->
        <div class="sidebar-brand-container">
            <?php if (!empty($logoToUse)): ?>
                <div class="navbar-brand mb-0 logo-container">
                    <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                         alt="<?php echo get_admin_title(); ?>"
                         class="sidebar-logo">
                </div>
            <?php else: ?>
                <h5 class="navbar-brand mb-0"><span class="navbar-brand-text"><?php echo get_admin_title(); ?></span></h5>
            <?php endif; ?>
        </div>

        <!-- Initials for collapsed sidebar -->
        <div class="sidebar-initials-container">
            <div class="sidebar-initials">
                <?php echo $siteInitials; ?>
            </div>
        </div>

        <!-- Internal toggle button for desktop -->
        <button class="sidebar-toggle-btn d-none d-md-block" id="sidebarToggleDesktop" title="Toggle Sidebar">
            <i class="bi bi-chevron-left"></i>
        </button>

        <!-- Mobile toggle button -->
        <button class="d-md-none btn btn-sm btn-outline-light" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('dashboard.php'); ?> href="<?php echo admin_url_for('dashboard.php'); ?>" title="<?php _e('dashboard'); ?>">
                    <i class="fas fa-tachometer-alt"></i> <span class="menu-text"><?php _e('dashboard'); ?></span>
                </a>
            </li>

            <!-- Member Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="fas fa-users"></i> <span class="menu-text"><?php _e('member_management'); ?></span>
                </div>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('members.php'); ?> href="<?php echo admin_url_for('members.php'); ?>" title="<?php _e('members'); ?>">
                    <i class="fas fa-users"></i> <span class="menu-text"><?php _e('members'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('add_member.php'); ?> href="<?php echo admin_url_for('add_member.php'); ?>" title="<?php _e('add_member'); ?>">
                    <i class="fas fa-user-plus"></i> <span class="menu-text"><?php _e('add_member'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('volunteer_opportunities.php'); ?> href="<?php echo admin_url_for('volunteer_opportunities.php'); ?>" title="Volunteer Opportunities">
                    <i class="fas fa-hands-helping"></i> <span class="menu-text">Volunteer Opportunities</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('requests.php'); ?> href="<?php echo admin_url_for('requests.php'); ?>" title="Requests">
                    <i class="fas fa-heart"></i> <span class="menu-text">Requests</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('family_management.php'); ?> href="<?php echo admin_url_for('family_management.php'); ?>" title="Family Management">
                    <i class="fas fa-users"></i> <span class="menu-text">Family Management</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('member_skills.php'); ?> href="<?php echo admin_url_for('member_skills.php'); ?>" title="Member Skills">
                    <i class="fas fa-tools"></i> <span class="menu-text">Member Skills</span>
                </a>
            </li>

            <!-- Events Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="fas fa-calendar-alt"></i> <span class="menu-text"><?php _e('events_management'); ?></span>
                </div>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('events.php'); ?> href="<?php echo admin_url_for('events.php'); ?>" title="<?php _e('events'); ?>">
                    <i class="fas fa-calendar-alt"></i> <span class="menu-text"><?php _e('events'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('event_attendance.php'); ?> href="<?php echo admin_url_for('event_attendance.php'); ?>" title="<?php _e('event_attendance'); ?>">
                    <i class="fas fa-calendar-check"></i> <span class="menu-text"><?php _e('event_attendance'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('event_categories.php'); ?> href="<?php echo admin_url_for('event_categories.php'); ?>" title="<?php _e('event_categories'); ?>">
                    <i class="bi bi-tags"></i> <span class="menu-text"><?php _e('event_categories'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('event_reports.php'); ?> href="<?php echo admin_url_for('event_reports.php'); ?>" title="<?php _e('event_reports'); ?>">
                    <i class="bi bi-file-earmark-bar-graph"></i> <span class="menu-text"><?php _e('event_reports'); ?></span>
                </a>
            </li>

            <!-- Email Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-envelope-fill"></i> <span class="menu-text"><?php _e('email_management'); ?></span>
                </div>
            </li>
            <!-- Email Communication -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('bulk_email.php'); ?> href="<?php echo admin_url_for('bulk_email.php'); ?>" title="<?php _e('bulk_email'); ?>">
                    <i class="bi bi-envelope-fill"></i> <span class="menu-text"><?php _e('bulk_email'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('email_scheduler.php'); ?> href="<?php echo admin_url_for('email_scheduler.php'); ?>" title="<?php _e('email_scheduler'); ?>">
                    <i class="bi bi-calendar-event"></i> <span class="menu-text"><?php _e('email_scheduler'); ?></span>
                </a>
            </li>

            <!-- Contact Management -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('contacts.php'); ?> href="<?php echo admin_url_for('contacts.php'); ?>" title="<?php _e('contacts'); ?>">
                    <i class="bi bi-person-lines-fill"></i> <span class="menu-text"><?php _e('contacts'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('contact_groups.php'); ?> href="<?php echo admin_url_for('contact_groups.php'); ?>" title="<?php _e('contact_groups'); ?>">
                    <i class="bi bi-folder"></i> <span class="menu-text"><?php _e('contact_groups'); ?></span>
                </a>
            </li>

            <!-- Birthday Management -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('birthday.php'); ?> href="<?php echo admin_url_for('birthday.php'); ?>" title="<?php _e('birthday_messages'); ?>">
                    <i class="bi bi-gift"></i> <span class="menu-text"><?php _e('birthday_messages'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('send_birthday_emails.php'); ?> href="<?php echo admin_url_for('send_birthday_emails.php'); ?>" title="<?php _e('send_birthday_emails'); ?>">
                    <i class="bi bi-envelope-paper"></i> <span class="menu-text"><?php _e('send_birthday_emails'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('test_birthday_email.php'); ?> href="<?php echo admin_url_for('test_birthday_email.php'); ?>" title="<?php _e('test_birthday_emails'); ?>">
                    <i class="bi bi-envelope-check"></i> <span class="menu-text"><?php _e('test_birthday_emails'); ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('send_birthday_notification.php'); ?> href="<?php echo admin_url_for('send_birthday_notification.php'); ?>" title="<?php _e('birthday_notifications'); ?>">
                    <i class="bi bi-bell"></i> <span class="menu-text"><?php _e('birthday_notifications'); ?></span>
                </a>
            </li>

            <!-- Email Templates -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('email_templates.php'); ?> href="<?php echo admin_url_for('email_templates.php'); ?>" title="<?php _e('email_templates'); ?>">
                    <i class="bi bi-envelope"></i> <span class="menu-text"><?php _e('email_templates'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('automated_email_templates.php'); ?> href="<?php echo admin_url_for('automated_email_templates.php'); ?>" title="<?php _e('automated_templates'); ?>">
                    <i class="bi bi-clock"></i> <span class="menu-text"><?php _e('automated_templates'); ?></span>
                </a>
            </li>

            <!-- WhatsApp Integration -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('whatsapp_templates.php'); ?> href="<?php echo admin_url_for('whatsapp_templates.php'); ?>" title="<?php _e('whatsapp_templates'); ?>">
                    <i class="bi bi-whatsapp"></i> <span class="menu-text"><?php _e('whatsapp_templates'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('whatsapp_messages.php'); ?> href="<?php echo admin_url_for('whatsapp_messages.php'); ?>" title="<?php _e('whatsapp_messages'); ?>">
                    <i class="bi bi-whatsapp"></i> <span class="menu-text"><?php _e('whatsapp_messages'); ?></span>
                </a>
            </li>

            <!-- Analytics & Tools -->
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('email_analytics.php'); ?> href="<?php echo admin_url_for('email_analytics.php'); ?>" title="<?php _e('email_analytics'); ?>">
                    <i class="bi bi-graph-up"></i> <span class="menu-text"><?php _e('email_analytics'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('about_shortcodes.php'); ?> href="<?php echo admin_url_for('about_shortcodes.php'); ?>" title="<?php _e('about_shortcodes'); ?>">
                    <i class="bi bi-info-circle"></i> <span class="menu-text"><?php _e('about_shortcodes'); ?></span>
                </a>
            </li>

            <!-- SMS Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-chat-text-fill"></i> <span class="menu-text"><?php _e('sms_management'); ?></span>
                </div>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('single_sms.php'); ?> href="<?php echo admin_url_for('single_sms.php'); ?>" title="<?php _e('single_sms'); ?>">
                    <i class="bi bi-chat-text"></i> <span class="menu-text"><?php _e('single_sms'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('bulk_sms.php'); ?> href="<?php echo admin_url_for('bulk_sms.php'); ?>" title="<?php _e('bulk_sms'); ?>">
                    <i class="bi bi-chat-text-fill"></i> <span class="menu-text"><?php _e('bulk_sms'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('sms_campaigns.php'); ?> href="<?php echo admin_url_for('sms_campaigns.php'); ?>" title="<?php _e('sms_campaigns'); ?>">
                    <i class="bi bi-megaphone"></i> <span class="menu-text"><?php _e('sms_campaigns'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('sms_templates.php'); ?> href="<?php echo admin_url_for('sms_templates.php'); ?>" title="<?php _e('sms_templates'); ?>">
                    <i class="bi bi-file-text"></i> <span class="menu-text"><?php _e('sms_templates'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('sms_analytics.php'); ?> href="<?php echo admin_url_for('sms_analytics.php'); ?>" title="<?php _e('sms_analytics'); ?>">
                    <i class="bi bi-graph-up"></i> <span class="menu-text"><?php _e('sms_analytics'); ?></span>
                </a>
            </li>

            <!-- Integrations -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-puzzle-fill"></i> <span class="menu-text"><?php _e('integrations'); ?></span>
                </div>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('calendar_integration.php'); ?> href="<?php echo admin_url_for('calendar_integration.php'); ?>" title="<?php _e('calendar_integration'); ?>">
                    <i class="bi bi-calendar-check"></i> <span class="menu-text"><?php _e('calendar_integration'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('social_media_integration.php'); ?> href="<?php echo admin_url_for('social_media_integration.php'); ?>" title="<?php _e('social_media'); ?>">
                    <i class="bi bi-share"></i> <span class="menu-text"><?php _e('social_media'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('sms_integration.php'); ?> href="<?php echo admin_url_for('sms_integration.php'); ?>" title="<?php _e('sms_integration'); ?>">
                    <i class="bi bi-chat-dots"></i> <span class="menu-text"><?php _e('sms_integration'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('payment_integration.php'); ?> href="<?php echo admin_url_for('payment_integration.php'); ?>" title="<?php _e('payment_integration'); ?>">
                    <i class="bi bi-credit-card"></i> <span class="menu-text"><?php _e('payment_integration'); ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('donations.php'); ?> href="<?php echo admin_url_for('donations.php'); ?>" title="<?php _e('donations'); ?>">
                    <i class="bi bi-cash-coin"></i> <span class="menu-text"><?php _e('donations'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('gift_management.php'); ?> href="<?php echo admin_url_for('gift_management.php'); ?>" title="Gift Management">
                    <i class="bi bi-gift-fill"></i> <span class="menu-text">Gift Management</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('send_gift_to_user.php'); ?> href="<?php echo admin_url_for('send_gift_to_user.php'); ?>" title="Send Gift to Member">
                    <i class="bi bi-send-fill"></i> <span class="menu-text">Send Gift to Member</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('enhanced_donate.php'); ?> href="<?php echo admin_url_for('enhanced_donate.php'); ?>" title="Enhanced Donation Form">
                    <i class="bi bi-heart-fill"></i> <span class="menu-text">Enhanced Donations</span>
                </a>
            </li>




            <!-- Account -->
            <li class="nav-item mt-2 mb-1 category-header">
                <div class="sidebar-heading px-3 text-muted text-uppercase small">
                    <i class="bi bi-gear-fill"></i> <span class="menu-text"><?php _e('account'); ?></span>
                </div>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('settings.php'); ?> href="<?php echo admin_url_for('settings.php'); ?>" title="<?php _e('settings'); ?>">
                    <i class="bi bi-gear-fill"></i> <span class="menu-text"><?php _e('settings'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('appearance_settings.php'); ?> href="<?php echo admin_url_for('appearance_settings.php'); ?>" title="<?php _e('appearance'); ?>">
                    <i class="bi bi-palette"></i> <span class="menu-text"><?php _e('appearance'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('custom_fields.php'); ?> href="<?php echo admin_url_for('custom_fields.php'); ?>" title="<?php _e('custom_fields'); ?>">
                    <i class="bi bi-ui-checks-grid"></i> <span class="menu-text"><?php _e('custom_fields'); ?></span>
                </a>
            </li>


            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('logo_management_consolidated.php'); ?> href="<?php echo admin_url_for('logo_management_consolidated.php'); ?>" title="<?php _e('logo_management'); ?>">
                    <i class="bi bi-image"></i> <span class="menu-text"><?php _e('logo_management'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('setup_security.php'); ?> href="<?php echo admin_url_for('setup_security.php'); ?>" title="<?php _e('security_setup'); ?>">
                    <i class="bi bi-shield-plus"></i> <span class="menu-text"><?php _e('security_setup'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('security_audit.php'); ?> href="<?php echo admin_url_for('security_audit.php'); ?>" title="<?php _e('security_audit'); ?>">
                    <i class="bi bi-shield-lock"></i> <span class="menu-text"><?php _e('security_audit'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('security_settings.php'); ?> href="<?php echo admin_url_for('security_settings.php'); ?>" title="<?php _e('security_settings'); ?>">
                    <i class="bi bi-shield-check"></i> <span class="menu-text"><?php _e('security_settings'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" <?php echo is_active('profile.php'); ?> href="<?php echo admin_url_for('profile.php'); ?>" title="<?php _e('my_profile'); ?>">
                    <i class="bi bi-person-circle"></i> <span class="menu-text"><?php _e('my_profile'); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo admin_url_for('logout.php'); ?>" title="<?php _e('logout'); ?>">
                    <i class="bi bi-box-arrow-right"></i> <span class="menu-text"><?php _e('logout'); ?></span>
                </a>
            </li>
        </ul>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const collapseToggle = document.getElementById('sidebarCollapseToggle');
    const desktopToggle = document.getElementById('sidebarToggleDesktop');
    const mainContent = document.querySelector('.main-content');
    const isMobile = window.innerWidth <= 768;

    // Mobile sidebar toggle
    if(sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('sidebar-collapsed');
        });
    }

    // Desktop sidebar toggle - Only apply for non-mobile
    if(!isMobile) {
        // Check for user preference in localStorage
        if(localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            if(collapseToggle && collapseToggle.querySelector('i')) {
                collapseToggle.querySelector('i').classList.remove('bi-chevron-left');
                collapseToggle.querySelector('i').classList.add('bi-chevron-right');
            }
        }

        // Enhanced desktop toggle functionality
        function toggleSidebar() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Update icon for external toggle
            const externalIcon = collapseToggle ? collapseToggle.querySelector('i') : null;
            if(externalIcon) {
                if(sidebar.classList.contains('collapsed')) {
                    externalIcon.classList.remove('bi-chevron-left');
                    externalIcon.classList.add('bi-chevron-right');
                } else {
                    externalIcon.classList.remove('bi-chevron-right');
                    externalIcon.classList.add('bi-chevron-left');
                }
            }

            // Save state
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed') ? 'true' : 'false');

            // Force window resize event to update any responsive components
            window.dispatchEvent(new Event('resize'));
        }

        // External collapse toggle (existing)
        if(collapseToggle) {
            collapseToggle.addEventListener('click', toggleSidebar);
        }

        // Internal desktop toggle (new)
        if(desktopToggle) {
            desktopToggle.addEventListener('click', toggleSidebar);
        }
    }

    // Smooth scrolling for sidebar content
    const sidebarContent = document.querySelector('.sidebar-content');
    if(sidebarContent) {
        sidebarContent.style.scrollBehavior = 'smooth';
    }
});
</script>