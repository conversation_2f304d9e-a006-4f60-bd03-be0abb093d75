<?php
/**
 * User Donation System
 *
 * Comprehensive donation system with birthday gifts, digital cards, scheduling, and organization integration
 * Renamed from enhanced_donate.php to donate.php for better naming
 */

// Start session for user authentication
session_start();

require_once '../config.php';

// Check if user is logged in (optional - allow both logged in and anonymous donations)
$isLoggedIn = isset($_SESSION['user_id']);
$userData = null;

if ($isLoggedIn) {
    // Get user data from members table
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Donation';
$organization_name = get_organization_name();
require_once '../includes/email_functions.php';

// Get organization information
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type', 'sender_name', 'sender_email')");
$stmt->execute();
$settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Check if donations are enabled
$donations_enabled = get_site_setting('donations_enabled', '1') === '1';

// Check if enhanced donations are enabled
$enhanced_donations_enabled = get_site_setting('enhanced_donations_enabled', '1') === '1';

// Get payment settings
$payment_settings = [
    'paypal_enabled' => get_site_setting('paypal_enabled', '0') === '1',
    'stripe_enabled' => get_site_setting('stripe_enabled', '0') === '1',
    'paystack_enabled' => get_site_setting('paystack_enabled', '0') === '1',
    'flutterwave_enabled' => get_site_setting('flutterwave_enabled', '0') === '1',
    'bank_transfer_enabled' => get_site_setting('bank_transfer_enabled', '0') === '1',
    'mobile_money_enabled' => get_site_setting('mobile_money_enabled', '0') === '1'
];

// Get currency settings
$default_currency = get_site_setting('default_currency', 'USD');
$supported_currencies = json_decode(get_site_setting('supported_currencies', '["USD"]'), true);

// Get members for birthday gifts (if enhanced donations enabled)
$members = [];
if ($enhanced_donations_enabled) {
    try {
        $stmt = $pdo->prepare("SELECT id, full_name, email, date_of_birth FROM members WHERE status = 'active' ORDER BY full_name");
        $stmt->execute();
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Handle case where members table structure is different
        try {
            $stmt = $pdo->prepare("SELECT id, full_name, email, birth_date as date_of_birth FROM members WHERE status = 'active' ORDER BY full_name");
            $stmt->execute();
            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e2) {
            $members = [];
        }
    }
}

// Get upcoming birthdays for suggestions
$upcoming_birthdays = [];
if ($enhanced_donations_enabled && !empty($members)) {
    try {
        $stmt = $pdo->prepare("
            SELECT id, full_name, email,
                   COALESCE(date_of_birth, birth_date) as date_of_birth,
                   CASE
                       WHEN DATE_FORMAT(COALESCE(date_of_birth, birth_date), '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d')
                       THEN DATEDIFF(STR_TO_DATE(CONCAT(YEAR(CURDATE()), '-', DATE_FORMAT(COALESCE(date_of_birth, birth_date), '%m-%d')), '%Y-%m-%d'), CURDATE())
                       ELSE DATEDIFF(STR_TO_DATE(CONCAT(YEAR(CURDATE()) + 1, '-', DATE_FORMAT(COALESCE(date_of_birth, birth_date), '%m-%d')), '%Y-%m-%d'), CURDATE())
                   END as days_until_birthday
            FROM members
            WHERE status = 'active'
            AND (date_of_birth IS NOT NULL OR birth_date IS NOT NULL)
            HAVING days_until_birthday <= 30
            ORDER BY days_until_birthday ASC
            LIMIT 10
        ");
        $stmt->execute();
        $upcoming_birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $upcoming_birthdays = [];
    }
}

// Get donation categories (with error handling)
$donation_categories = [];
if ($enhanced_donations_enabled) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM donation_categories WHERE is_active = 1 ORDER BY sort_order, category_name");
        $stmt->execute();
        $donation_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Table doesn't exist, continue without categories
        $donation_categories = [];
    }
}

// Get gift templates (with error handling)
$gift_templates = [];
if ($enhanced_donations_enabled) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM gift_templates WHERE is_active = 1 ORDER BY template_category, template_name");
        $stmt->execute();
        $gift_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Table doesn't exist, continue without templates
        $gift_templates = [];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-text: #2c3e50;
        }

        body {
            background: linear-gradient(135deg, var(--light-bg) 0%, #bdc3c7 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-logo {
            height: 40px;
            width: auto;
        }

        .donation-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .donation-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px 15px 0 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .donation-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="rgba(255,255,255,0.1)" font-size="12">♥</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
            opacity: 0.1;
        }

        .donation-header h1 {
            position: relative;
            z-index: 1;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .donation-header p {
            position: relative;
            z-index: 1;
            margin-bottom: 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .donation-card {
            background: white;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .form-section {
            padding: 2rem;
            display: none;
        }

        .form-section.active {
            display: block;
        }

        .gift-type-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .gift-type-card:hover {
            border-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .gift-type-card.selected {
            border-color: var(--secondary-color);
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(52, 152, 219, 0.05) 100%);
        }

        .donation-option {
            transition: all 0.3s ease;
        }

        .donation-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        @media (max-width: 768px) {
            .donation-container {
                margin: 1rem auto;
                padding: 0 0.5rem;
            }

            .donation-header {
                padding: 1.5rem;
            }

            .form-section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php if ($isLoggedIn): ?>
        <?php include 'includes/navbar.php'; ?>
    <?php else: ?>
        <!-- Simple navbar for anonymous users -->
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="../register.php">
                    <?php
                    // Use the existing logo management system
                    $headerLogo = get_site_setting('header_logo', '');
                    $mainLogo = get_site_setting('main_logo', '');
                    $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

                    if (!empty($logoToUse)): ?>
                        <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                             alt="<?php echo get_organization_name(); ?>"
                             class="navbar-logo me-2">
                        <?php echo htmlspecialchars(get_organization_name()); ?>
                    <?php else: ?>
                        <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($organization_name); ?>
                    <?php endif; ?>
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="login.php" style="color: rgba(255,255,255,0.9);">
                        <i class="bi bi-box-arrow-in-right"></i> Login
                    </a>
                </div>
            </div>
        </nav>
    <?php endif; ?>

    <div class="container donation-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- Donation Header -->
                <div class="donation-header">
                    <h1><i class="bi bi-heart-fill"></i> Donation System</h1>
                    <p class="lead">Support <?php echo htmlspecialchars($organization_name); ?> with flexible giving options</p>
                </div>

                <div class="donation-card">
                    <div class="form-section active">
                        <?php if (!$donations_enabled): ?>
                            <div class="alert alert-warning">
                                <h4>Donations are currently disabled</h4>
                                <p>The donation system is currently disabled. Please contact the administrator for more information.</p>
                            </div>
                        <?php else: ?>
                            <h5>Select Donation Type</h5>
                            <div class="row g-3">
                                <!-- General Donation -->
                                <div class="col-md-6">
                                    <div class="gift-type-card donation-option h-100 p-3" data-type="general">
                                        <div class="text-center">
                                            <i class="bi bi-heart-fill" style="font-size: 2rem; color: var(--bs-primary);"></i>
                                            <h5 class="mt-2">General Donation</h5>
                                            <p class="text-muted">Support our mission and community</p>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($enhanced_donations_enabled && !empty($members)): ?>
                                <!-- Birthday Gift -->
                                <div class="col-md-6">
                                    <div class="gift-type-card donation-option h-100 p-3" data-type="birthday_gift">
                                        <div class="text-center">
                                            <i class="bi bi-gift-fill" style="font-size: 2rem; color: var(--bs-warning);"></i>
                                            <h5 class="mt-2">Birthday Gift</h5>
                                            <p class="text-muted">Send a special gift to celebrate someone's birthday</p>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="button" class="btn btn-primary btn-lg" onclick="alert('Donation functionality would be implemented here')">
                                    Proceed with Donation <i class="bi bi-arrow-right"></i>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
