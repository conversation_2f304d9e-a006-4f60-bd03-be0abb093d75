<?php
/**
 * User Footer
 * 
 * Beautiful footer for user interface pages with organization info and links
 */

// Get organization settings
$organizationName = get_organization_name();
$currentYear = date('Y');

// Get some basic settings for footer
$footerLogo = get_site_setting('footer_logo', '');
$mainLogo = get_site_setting('main_logo', '');
$logoToUse = !empty($footerLogo) ? $footerLogo : $mainLogo;

// Get contact information if available
$contactEmail = get_site_setting('contact_email', '');
$contactPhone = get_site_setting('contact_phone', '');
$address = get_site_setting('organization_address', '');
?>

<footer class="user-footer mt-5">
    <div class="footer-content">
        <div class="container">
            <div class="row">
                <!-- Organization Info -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <?php if (!empty($logoToUse)): ?>
                            <div class="footer-logo mb-3">
                                <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>" 
                                     alt="<?php echo htmlspecialchars($organizationName); ?>" 
                                     class="footer-logo-img">
                            </div>
                        <?php endif; ?>
                        
                        <h5 class="footer-title">
                            <i class="bi bi-house-heart me-2"></i>
                            <?php echo htmlspecialchars($organizationName); ?>
                        </h5>
                        
                        <p class="footer-description">
                            <?php echo htmlspecialchars(get_site_setting('organization_mission', 'Building community and serving together in love and fellowship.')); ?>
                        </p>
                        
                        <?php if (!empty($address)): ?>
                            <div class="footer-contact-item">
                                <i class="bi bi-geo-alt"></i>
                                <span><?php echo htmlspecialchars($address); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6 class="footer-section-title">Quick Links</h6>
                        <ul class="footer-links">
                            <li><a href="dashboard.php"><i class="bi bi-speedometer2"></i> Dashboard</a></li>
                            <li><a href="events.php"><i class="bi bi-calendar-event"></i> Events</a></li>
                            <li><a href="requests.php"><i class="bi bi-chat-heart"></i> Requests</a></li>
                            <li><a href="my_gifts.php"><i class="bi bi-gift"></i> My Gifts</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Community -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6 class="footer-section-title">Community</h6>
                        <ul class="footer-links">
                            <li><a href="volunteer_opportunities.php"><i class="bi bi-person-workspace"></i> Volunteer</a></li>
                            <li><a href="skills.php"><i class="bi bi-tools"></i> Skills</a></li>
                            <li><a href="family_management.php"><i class="bi bi-people"></i> Family</a></li>
                            <li><a href="../donate.php"><i class="bi bi-heart"></i> Donation</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Contact & Support -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-section">
                        <h6 class="footer-section-title">Contact & Support</h6>
                        
                        <?php if (!empty($contactEmail)): ?>
                            <div class="footer-contact-item">
                                <i class="bi bi-envelope"></i>
                                <a href="mailto:<?php echo htmlspecialchars($contactEmail); ?>">
                                    <?php echo htmlspecialchars($contactEmail); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($contactPhone)): ?>
                            <div class="footer-contact-item">
                                <i class="bi bi-telephone"></i>
                                <a href="tel:<?php echo htmlspecialchars($contactPhone); ?>">
                                    <?php echo htmlspecialchars($contactPhone); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="footer-links mt-3">
                            <a href="profile.php"><i class="bi bi-person"></i> My Profile</a>
                            <a href="settings.php"><i class="bi bi-gear"></i> Settings</a>
                            <a href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer Bottom -->
    <div class="footer-bottom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="footer-copyright">
                        <i class="bi bi-c-circle me-1"></i>
                        <?php echo $currentYear; ?> <?php echo htmlspecialchars($organizationName); ?>. 
                        <span class="text-muted">All rights reserved.</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="footer-social text-md-end">
                        <span class="footer-tagline">
                            <i class="bi bi-heart-fill text-danger me-1"></i>
                            Made with love for our community
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
/* Footer Styles */
.user-footer {
    background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
    color: white;
    margin-top: auto;
}

.footer-content {
    padding: 3rem 0 2rem;
}

.footer-logo-img {
    max-height: 60px;
    max-width: 200px;
    height: auto;
    width: auto;
    object-fit: contain;
    filter: brightness(0) invert(1); /* Make logo white */
}

.footer-title {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section-title {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
}

.footer-links a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-links a i {
    width: 16px;
    margin-right: 8px;
    font-size: 0.85rem;
}

.footer-contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

.footer-contact-item i {
    width: 20px;
    margin-right: 10px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-contact-item a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-contact-item a:hover {
    color: white;
}

.footer-bottom {
    background: rgba(0, 0, 0, 0.2);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.footer-social {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.footer-tagline {
    display: inline-flex;
    align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .footer-content {
        padding: 2rem 0 1.5rem;
    }
    
    .footer-social {
        text-align: center !important;
        margin-top: 1rem;
    }
    
    .footer-logo-img {
        max-height: 50px;
        max-width: 150px;
    }
}

/* Ensure footer sticks to bottom */
html, body {
    height: 100%;
}

body {
    display: flex;
    flex-direction: column;
}

.container, .container-fluid {
    flex: 1;
}
</style>
