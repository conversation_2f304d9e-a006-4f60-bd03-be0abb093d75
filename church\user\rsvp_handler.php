<?php
/**
 * User RSVP Handler
 * 
 * Handles RSVP submissions from authenticated users
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}

$userId = $_SESSION['user_id'];

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Note: CSRF token validation temporarily disabled for testing
// TODO: Implement CSRF token in the form

// Get and validate input
$eventId = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
$status = isset($_POST['status']) ? trim($_POST['status']) : '';
$notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

if (!$eventId || !$status) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

// Validate status
$validStatuses = ['attending', 'maybe', 'not_attending'];
if (!in_array($status, $validStatuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid RSVP status']);
    exit();
}

try {
    // Check if event exists and is active (try both is_active and status columns)
    try {
        // First try with is_active column
        $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If is_active column doesn't exist, try with status column
        try {
            $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND status = 'published'");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e2) {
            // If neither column exists, just check if event exists
            $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }

    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found']);
        exit();
    }
    
    // Check if event is in the future (event_date is already a datetime)
    if (strtotime($event['event_date']) < time()) {
        echo json_encode(['success' => false, 'message' => 'Cannot RSVP to past events']);
        exit();
    }
    
    // Check if user already has an RSVP for this event
    $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $userId]);
    $existingRsvp = $stmt->fetch();
    
    if ($existingRsvp) {
        // Update existing RSVP
        $stmt = $pdo->prepare("
            UPDATE event_rsvps 
            SET status = ?, notes = ?, updated_at = NOW()
            WHERE event_id = ? AND user_id = ?
        ");
        $stmt->execute([$status, $notes, $eventId, $userId]);
        $message = 'RSVP updated successfully!';
    } else {
        // Check capacity for new attendees
        if ($status === 'attending' && $event['max_attendees']) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as attending_count 
                FROM event_rsvps 
                WHERE event_id = ? AND status = 'attending'
            ");
            $stmt->execute([$eventId]);
            $attendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['attending_count'];
            
            if ($attendingCount >= $event['max_attendees']) {
                echo json_encode([
                    'success' => false, 
                    'message' => 'Event is full. Please try joining the waitlist.'
                ]);
                exit();
            }
        }
        
        // Create new RSVP
        $stmt = $pdo->prepare("
            INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([$eventId, $userId, $status, $notes]);
        $message = 'RSVP submitted successfully!';
    }
    
    // Get updated counts for response
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending_count,
            COUNT(CASE WHEN status = 'maybe' THEN 1 END) as maybe_count,
            COUNT(CASE WHEN status = 'not_attending' THEN 1 END) as not_attending_count
        FROM event_rsvps 
        WHERE event_id = ?
    ");
    $stmt->execute([$eventId]);
    $counts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'status' => $status,
        'counts' => $counts
    ]);
    
} catch (PDOException $e) {
    error_log("RSVP Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred', 'debug' => $e->getMessage()]);
} catch (Exception $e) {
    error_log("RSVP General Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred', 'debug' => $e->getMessage()]);
}
?>
