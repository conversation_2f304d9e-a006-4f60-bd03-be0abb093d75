<?php
/**
 * Custom Field Helper Class
 * Provides methods to integrate custom fields into forms and data handling
 */

class CustomFieldHelper {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get custom fields for an entity type
     */
    public function getCustomFields($entityType, $activeOnly = true) {
        $sql = "SELECT * FROM custom_field_definitions WHERE entity_type = ?";
        $params = [$entityType];
        
        if ($activeOnly) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY field_order ASC, created_at ASC";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get custom field values for a specific entity
     */
    public function getCustomFieldValues($entityType, $entityId) {
        $sql = "SELECT cfd.field_name, cfv.field_value, cfd.field_type, cfd.field_options
                FROM custom_field_definitions cfd
                LEFT JOIN custom_field_values cfv ON cfd.id = cfv.field_definition_id AND cfv.entity_id = ?
                WHERE cfd.entity_type = ? AND cfd.is_active = 1
                ORDER BY cfd.field_order ASC";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$entityId, $entityType]);
        $results = $stmt->fetchAll();
        
        $values = [];
        foreach ($results as $row) {
            $values[$row['field_name']] = [
                'value' => $row['field_value'],
                'type' => $row['field_type'],
                'options' => $row['field_options'] ? json_decode($row['field_options'], true) : null
            ];
        }
        
        return $values;
    }
    
    /**
     * Render custom field form inputs
     */
    public function renderCustomFieldInputs($entityType, $entityId = null, $values = []) {
        $fields = $this->getCustomFields($entityType);
        $existingValues = $entityId ? $this->getCustomFieldValues($entityType, $entityId) : [];
        
        $html = '';
        
        if (!empty($fields)) {
            $html .= '<div class="custom-fields-section">';
            $html .= '<h5 class="mb-3"><i class="bi bi-ui-checks-grid"></i> Additional Information</h5>';
            $html .= '<div class="row">';
            
            foreach ($fields as $field) {
                $fieldName = 'custom_field_' . $field['field_name'];
                $currentValue = $values[$fieldName] ?? ($existingValues[$field['field_name']]['value'] ?? $field['default_value']);
                
                $html .= $this->renderFieldInput($field, $fieldName, $currentValue);
            }
            
            $html .= '</div>';
            $html .= '</div>';
        }
        
        return $html;
    }
    
    /**
     * Render individual field input
     */
    private function renderFieldInput($field, $fieldName, $currentValue = '') {
        $required = $field['is_required'] ? 'required' : '';
        $helpText = $field['help_text'] ? '<div class="form-text">' . htmlspecialchars($field['help_text']) . '</div>' : '';
        $label = htmlspecialchars($field['field_label']);
        $requiredMark = $field['is_required'] ? ' <span class="text-danger">*</span>' : '';
        
        $html = '<div class="col-md-6 mb-3">';
        $html .= '<label for="' . $fieldName . '" class="form-label">' . $label . $requiredMark . '</label>';
        
        switch ($field['field_type']) {
            case 'text':
            case 'email':
            case 'url':
                $type = $field['field_type'] === 'text' ? 'text' : $field['field_type'];
                $html .= '<input type="' . $type . '" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . htmlspecialchars($currentValue) . '" ' . $required . '>';
                break;
                
            case 'phone':
                $html .= '<input type="tel" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . htmlspecialchars($currentValue) . '" ' . $required . '>';
                break;
                
            case 'number':
                $html .= '<input type="number" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . htmlspecialchars($currentValue) . '" ' . $required . '>';
                break;
                
            case 'date':
                $html .= '<input type="date" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . htmlspecialchars($currentValue) . '" ' . $required . '>';
                break;
                
            case 'datetime':
                $html .= '<input type="datetime-local" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . htmlspecialchars($currentValue) . '" ' . $required . '>';
                break;
                
            case 'textarea':
                $html .= '<textarea class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" rows="3" ' . $required . '>' . htmlspecialchars($currentValue) . '</textarea>';
                break;
                
            case 'select':
                $html .= '<select class="form-select" id="' . $fieldName . '" name="' . $fieldName . '" ' . $required . '>';
                $html .= '<option value="">Select...</option>';
                if ($field['field_options']) {
                    $options = json_decode($field['field_options'], true);
                    foreach ($options as $key => $value) {
                        $selected = $currentValue == $key ? 'selected' : '';
                        $html .= '<option value="' . htmlspecialchars($key) . '" ' . $selected . '>' . htmlspecialchars($value) . '</option>';
                    }
                }
                $html .= '</select>';
                break;
                
            case 'multiselect':
                $html .= '<select class="form-select" id="' . $fieldName . '" name="' . $fieldName . '[]" multiple ' . $required . '>';
                if ($field['field_options']) {
                    $options = json_decode($field['field_options'], true);
                    $selectedValues = is_array($currentValue) ? $currentValue : ($currentValue ? explode(',', $currentValue) : []);
                    foreach ($options as $key => $value) {
                        $selected = in_array($key, $selectedValues) ? 'selected' : '';
                        $html .= '<option value="' . htmlspecialchars($key) . '" ' . $selected . '>' . htmlspecialchars($value) . '</option>';
                    }
                }
                $html .= '</select>';
                break;
                
            case 'radio':
                if ($field['field_options']) {
                    $options = json_decode($field['field_options'], true);
                    foreach ($options as $key => $value) {
                        $checked = $currentValue == $key ? 'checked' : '';
                        $html .= '<div class="form-check">';
                        $html .= '<input class="form-check-input" type="radio" id="' . $fieldName . '_' . $key . '" name="' . $fieldName . '" value="' . htmlspecialchars($key) . '" ' . $checked . ' ' . $required . '>';
                        $html .= '<label class="form-check-label" for="' . $fieldName . '_' . $key . '">' . htmlspecialchars($value) . '</label>';
                        $html .= '</div>';
                    }
                }
                break;
                
            case 'checkbox':
                $checked = $currentValue ? 'checked' : '';
                $html .= '<div class="form-check">';
                $html .= '<input class="form-check-input" type="checkbox" id="' . $fieldName . '" name="' . $fieldName . '" value="1" ' . $checked . '>';
                $html .= '<label class="form-check-label" for="' . $fieldName . '">' . $label . '</label>';
                $html .= '</div>';
                break;
                
            case 'file':
                $html .= '<input type="file" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" ' . $required . '>';
                if ($currentValue) {
                    $html .= '<div class="form-text">Current file: <a href="' . htmlspecialchars($currentValue) . '" target="_blank">View</a></div>';
                }
                break;
        }
        
        $html .= $helpText;
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Save custom field values
     */
    public function saveCustomFieldValues($entityType, $entityId, $postData) {
        $fields = $this->getCustomFields($entityType);
        
        foreach ($fields as $field) {
            $fieldName = 'custom_field_' . $field['field_name'];
            
            if (isset($postData[$fieldName])) {
                $value = $postData[$fieldName];
                
                // Handle array values (multiselect)
                if (is_array($value)) {
                    $value = implode(',', $value);
                }
                
                // Insert or update field value
                $stmt = $this->pdo->prepare("
                    INSERT INTO custom_field_values (field_definition_id, entity_id, field_value)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE field_value = VALUES(field_value), updated_at = CURRENT_TIMESTAMP
                ");
                
                $stmt->execute([$field['id'], $entityId, $value]);
            }
        }
    }
    
    /**
     * Get custom field values for display in lists
     */
    public function getListDisplayFields($entityType) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM custom_field_definitions 
            WHERE entity_type = ? AND is_active = 1 AND is_visible_in_list = 1
            ORDER BY field_order ASC
        ");
        $stmt->execute([$entityType]);
        return $stmt->fetchAll();
    }
    
    /**
     * Format field value for display
     */
    public function formatFieldValue($field, $value) {
        if (empty($value)) {
            return '-';
        }
        
        switch ($field['field_type']) {
            case 'select':
            case 'radio':
                if ($field['field_options']) {
                    $options = json_decode($field['field_options'], true);
                    return $options[$value] ?? $value;
                }
                return $value;
                
            case 'multiselect':
                if ($field['field_options']) {
                    $options = json_decode($field['field_options'], true);
                    $values = explode(',', $value);
                    $displayValues = [];
                    foreach ($values as $val) {
                        $displayValues[] = $options[trim($val)] ?? trim($val);
                    }
                    return implode(', ', $displayValues);
                }
                return $value;
                
            case 'checkbox':
                return $value ? 'Yes' : 'No';
                
            case 'date':
                return date('M j, Y', strtotime($value));
                
            case 'datetime':
                return date('M j, Y g:i A', strtotime($value));
                
            case 'url':
                return '<a href="' . htmlspecialchars($value) . '" target="_blank">' . htmlspecialchars($value) . '</a>';
                
            case 'email':
                return '<a href="mailto:' . htmlspecialchars($value) . '">' . htmlspecialchars($value) . '</a>';
                
            case 'phone':
                return '<a href="tel:' . htmlspecialchars($value) . '">' . htmlspecialchars($value) . '</a>';
                
            case 'file':
                return '<a href="' . htmlspecialchars($value) . '" target="_blank">View File</a>';
                
            default:
                return htmlspecialchars($value);
        }
    }
    
    /**
     * Validate custom field values
     */
    public function validateCustomFields($entityType, $postData) {
        $fields = $this->getCustomFields($entityType);
        $errors = [];
        
        foreach ($fields as $field) {
            $fieldName = 'custom_field_' . $field['field_name'];
            $value = $postData[$fieldName] ?? '';
            
            // Check required fields
            if ($field['is_required'] && empty($value)) {
                $errors[] = $field['field_label'] . ' is required.';
                continue;
            }
            
            // Validate based on field type
            if (!empty($value)) {
                switch ($field['field_type']) {
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[] = $field['field_label'] . ' must be a valid email address.';
                        }
                        break;
                        
                    case 'url':
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $errors[] = $field['field_label'] . ' must be a valid URL.';
                        }
                        break;
                        
                    case 'number':
                        if (!is_numeric($value)) {
                            $errors[] = $field['field_label'] . ' must be a number.';
                        }
                        break;
                }
                
                // Apply custom validation rules
                if ($field['validation_rules']) {
                    $rules = json_decode($field['validation_rules'], true);
                    if ($rules) {
                        if (isset($rules['min']) && strlen($value) < $rules['min']) {
                            $errors[] = $field['field_label'] . ' must be at least ' . $rules['min'] . ' characters.';
                        }
                        if (isset($rules['max']) && strlen($value) > $rules['max']) {
                            $errors[] = $field['field_label'] . ' must be no more than ' . $rules['max'] . ' characters.';
                        }
                        if (isset($rules['pattern']) && !preg_match('/' . $rules['pattern'] . '/', $value)) {
                            $errors[] = $field['field_label'] . ' format is invalid.';
                        }
                    }
                }
            }
        }
        
        return $errors;
    }
}
?>
