<?php
/**
 * Download Gift Card Generator
 * Generates downloadable PDF and JPEG gift cards
 */

// Suppress warnings for clean output
error_reporting(E_ERROR | E_PARSE);

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

// Get parameters
$gift_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$format = isset($_GET['format']) ? $_GET['format'] : 'pdf';

if (!$gift_id) {
    die('Invalid gift ID');
}

// Get gift data
$stmt = $pdo->prepare("
    SELECT mg.*, 
           sender.full_name as sender_name, 
           recipient.full_name as recipient_name,
           recipient.first_name as recipient_first_name
    FROM member_gifts mg
    LEFT JOIN members sender ON mg.sender_id = sender.id
    LEFT JOIN members recipient ON mg.recipient_id = recipient.id
    WHERE mg.id = ? AND (mg.sender_id = ? OR mg.recipient_id = ?)
");
$stmt->execute([$gift_id, $_SESSION['user_id'], $_SESSION['user_id']]);
$gift = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$gift) {
    die('Gift not found or access denied');
}

// Get organization details
$org_name = get_organization_name();
$org_logo = get_site_setting('main_logo', '');

// Determine if user is sender or recipient
$is_sender = ($gift['sender_id'] == $_SESSION['user_id']);
$display_name = $is_sender ? $gift['recipient_name'] : $gift['sender_name'];
$from_to_text = $is_sender ? 'To: ' . $gift['recipient_name'] : 'From: ' . ($gift['is_anonymous'] ? 'Anonymous' : $gift['sender_name']);

if ($format === 'pdf') {
    generatePDF($gift, $org_name, $org_logo, $from_to_text);
} else {
    generateJPEG($gift, $org_name, $org_logo, $from_to_text);
}

function generatePDF($gift, $org_name, $org_logo, $from_to_text) {
    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Try to use DomPDF if available
    $pdf_generated = false;

    // Check for DomPDF in multiple possible locations
    $dompdf_paths = [
        '../vendor/autoload.php',
        '../../vendor/autoload.php',
        '../../../vendor/autoload.php'
    ];

    foreach ($dompdf_paths as $path) {
        if (file_exists($path)) {
            try {
                require_once $path;
                if (class_exists('Dompdf\Dompdf')) {
                    // Create HTML content for PDF
                    $html = generatePDFHTML($gift, $org_name, $from_to_text);

                    $dompdf = new Dompdf\Dompdf();
                    $dompdf->loadHtml($html);
                    $dompdf->setPaper('A4', 'portrait');
                    $dompdf->render();

                    header('Content-Type: application/pdf');
                    header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.pdf"');
                    header('Content-Length: ' . strlen($dompdf->output()));

                    echo $dompdf->output();
                    $pdf_generated = true;
                    exit(); // Important: exit after successful PDF generation
                }
            } catch (Exception $e) {
                error_log("DomPDF error: " . $e->getMessage());
                continue;
            }
        }
    }

    // If PDF generation failed, create an HTML file that can be printed to PDF
    if (!$pdf_generated) {
        generatePrintableHTML($gift, $org_name, $from_to_text);
    }
}

function generatePDFHTML($gift, $org_name, $from_to_text) {
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .gift-card {
            background: white;
            max-width: 600px;
            margin: 0 auto;
            border: 3px solid #667eea;
            border-radius: 15px;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        .header .org-name {
            font-size: 1.2em;
            margin-top: 10px;
        }
        .content {
            padding: 40px;
        }
        .gift-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .gift-message {
            font-size: 1.2em;
            line-height: 1.6;
            color: #555;
            margin-bottom: 30px;
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .gift-details {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            text-align: center;
        }
        .detail-item {
            display: inline-block;
            margin: 0 20px;
            text-align: center;
        }
        .detail-label {
            font-weight: bold;
            color: #667eea;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .detail-value {
            font-size: 1.1em;
            color: #333;
            margin-top: 5px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 0.9em;
        }
        .gift-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="gift-card">
        <div class="header">
            <div class="gift-icon">🎁</div>
            <h1>Gift Card</h1>
            <div class="org-name">' . htmlspecialchars($org_name) . '</div>
        </div>
        <div class="content">
            <div class="gift-title">' . htmlspecialchars($gift['gift_title']) . '</div>
            <div class="gift-message">' . nl2br(htmlspecialchars($gift['gift_message'])) . '</div>
            <div class="gift-details">
                <div class="detail-item">
                    <div class="detail-label">' . ($gift['sender_id'] == $_SESSION['user_id'] ? 'To' : 'From') . '</div>
                    <div class="detail-value">' . htmlspecialchars($from_to_text) . '</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Type</div>
                    <div class="detail-value">' . ucfirst(htmlspecialchars($gift['gift_type'])) . '</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Date</div>
                    <div class="detail-value">' . date('M j, Y', strtotime($gift['created_at'])) . '</div>
                </div>
            </div>
        </div>
        <div class="footer">
            Generated from ' . htmlspecialchars($org_name) . ' Gift Management System<br>
            ' . date('Y-m-d H:i:s') . '
        </div>
    </div>
</body>
</html>';
}

function generatePrintableHTML($gift, $org_name, $from_to_text) {
    // Create a printable HTML file that can be saved as PDF by the browser
    $html = generatePDFHTML($gift, $org_name, $from_to_text);

    // Add print-specific styles
    $html = str_replace('</head>', '
    <style media="print">
        @page {
            size: A4;
            margin: 20mm;
        }
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .no-print {
            display: none;
        }
    </style>
    <script>
        window.onload = function() {
            // Auto-open print dialog
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
    </head>', $html);

    // Add a print instruction at the top
    $html = str_replace('<body>', '<body>
    <div class="no-print" style="background: #f0f0f0; padding: 10px; margin-bottom: 20px; border-radius: 5px; text-align: center;">
        <strong>Print Instructions:</strong> Use your browser\'s print function (Ctrl+P) and select "Save as PDF" to create a PDF file.
        <br><small>This message will not appear in the printed version.</small>
    </div>', $html);

    // Set headers for HTML download
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.html"');

    echo $html;
    exit();
}

function generateJPEG($gift, $org_name, $org_logo, $from_to_text) {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        // Fallback to a simple text file if GD is not available
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.txt"');
        echo "GIFT CARD\n";
        echo "=========\n\n";
        echo "From: " . $org_name . "\n";
        echo "Title: " . $gift['gift_title'] . "\n";
        echo "Message: " . $gift['gift_message'] . "\n";
        echo $from_to_text . "\n";
        echo "Date: " . date('M j, Y', strtotime($gift['created_at'])) . "\n";
        exit();
    }

    // Clear any previous output
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Create image canvas
    $width = 800;
    $height = 600;
    $image = imagecreatetruecolor($width, $height);

    if (!$image) {
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="gift-card-error.txt"');
        echo "Error: Failed to create image canvas. GD extension may not be properly configured.";
        exit();
    }

    // Define colors
    $white = imagecolorallocate($image, 255, 255, 255);
    $primary = imagecolorallocate($image, 102, 126, 234);
    $secondary = imagecolorallocate($image, 118, 75, 162);
    $dark = imagecolorallocate($image, 51, 51, 51);
    $gray = imagecolorallocate($image, 108, 117, 125);
    $light_bg = imagecolorallocate($image, 248, 249, 250);

    // Fill background with white
    imagefill($image, 0, 0, $white);

    // Draw header background
    imagefilledrectangle($image, 0, 0, $width, 120, $primary);

    // Use built-in fonts (more reliable)
    $large_font = 5;
    $medium_font = 4;
    $small_font = 3;

    // Add title
    $title = "GIFT CARD";
    $title_width = imagefontwidth($large_font) * strlen($title);
    imagestring($image, $large_font, (int)(($width - $title_width) / 2), 30, $title, $white);

    // Add organization name
    $org_width = imagefontwidth($medium_font) * strlen($org_name);
    imagestring($image, $medium_font, (int)(($width - $org_width) / 2), 70, $org_name, $white);

    // Add decorative border
    imagerectangle($image, 20, 140, $width - 20, $height - 40, $primary);
    imagerectangle($image, 25, 145, $width - 25, $height - 45, $primary);

    // Add gift title
    $gift_title = strtoupper(substr($gift['gift_title'], 0, 50));
    $title_width = imagefontwidth($medium_font) * strlen($gift_title);
    imagestring($image, $medium_font, (int)(($width - $title_width) / 2), 170, $gift_title, $dark);

    // Add gift message (wrapped)
    if (!empty($gift['gift_message'])) {
        $message = wordwrap($gift['gift_message'], 70, "\n");
        $lines = explode("\n", $message);
        $y_pos = 220;
        foreach (array_slice($lines, 0, 8) as $line) { // Limit to 8 lines
            $line_width = imagefontwidth($small_font) * strlen($line);
            imagestring($image, $small_font, (int)(($width - $line_width) / 2), $y_pos, $line, $gray);
            $y_pos += 20;
        }
    }

    // Add details at bottom
    $detail_y = $height - 100;
    imagestring($image, $small_font, 50, $detail_y, $from_to_text, $gray);
    imagestring($image, $small_font, 300, $detail_y, "Type: " . ucfirst($gift['gift_type']), $gray);
    imagestring($image, $small_font, 500, $detail_y, "Date: " . date('M j, Y', strtotime($gift['created_at'])), $gray);

    // Add footer
    $footer = "Generated from " . $org_name . " - " . date('Y-m-d H:i:s');
    $footer_width = imagefontwidth(2) * strlen($footer);
    imagestring($image, 2, (int)(($width - $footer_width) / 2), $height - 25, $footer, $gray);

    // Set headers for JPEG output
    header('Content-Type: image/jpeg');
    header('Content-Disposition: attachment; filename="gift-card-' . $gift['id'] . '.jpg"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

    // Output image directly
    $success = imagejpeg($image, null, 90);

    // Clean up
    imagedestroy($image);

    if (!$success) {
        // If JPEG generation failed, clear headers and output error
        header_remove();
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="gift-card-error.txt"');
        echo "Error generating JPEG image. Please try the PDF download instead.";
    }

    exit();
}
?>
