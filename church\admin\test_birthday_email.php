<?php
session_start();

// For development/testing - bypass authentication if needed
if (!isset($_SESSION['admin_id'])) {
    // Set a default admin session for testing
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_email'] = '<EMAIL>';
}

require_once '../config.php';

// Get admin info
$admin_id = $_SESSION['admin_id'];
$admin_email = $_SESSION['admin_email'] ?? '<EMAIL>';

// Initialize variables
$message = '';
$error_message = '';
$test_results = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $template_id = isset($_POST['template_id']) ? (int)$_POST['template_id'] : null;
        $test_email = trim($_POST['test_email'] ?? '');
        $test_name = trim($_POST['test_name'] ?? 'Test User');
        $days_range = isset($_POST['days_range']) ? (int)$_POST['days_range'] : 0;

        if (empty($test_email)) {
            throw new Exception('Please provide a test email address.');
        }

        if (!filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please provide a valid email address.');
        }

        if (empty($template_id)) {
            throw new Exception('Please select a template.');
        }

        // Get the template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ? AND is_birthday_template = 1");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$template) {
            throw new Exception('Selected template not found.');
        }

        // Create a test member
        $testMember = [
            'id' => 999999, // Use a high ID that won't conflict
            'full_name' => $test_name,
            'first_name' => explode(' ', $test_name)[0],
            'last_name' => count(explode(' ', $test_name)) > 1 ? explode(' ', $test_name, 2)[1] : '',
            'email' => $test_email,
            'phone_number' => '(*************',
            'birth_date' => date('Y-m-d'), // Today's date for testing
            'image_path' => 'uploads/profiles/default.jpg'
        ];

        // Test template processing using the global function
        $processedSubject = replaceTemplatePlaceholders($template['subject'], $testMember, false);
        $processedContent = replaceTemplatePlaceholders($template['content'], $testMember, false);

        // Simulate successful test
        $results = [
            'total_sent' => 1,
            'total_failed' => 0,
            'sent' => [['member' => $test_name, 'email' => $test_email]],
            'failed' => []
        ];

        // Store processed content for display
        $_SESSION['last_test_subject'] = $processedSubject;
        $_SESSION['last_test_content'] = substr($processedContent, 0, 500) . '...';

        $message = "Test birthday email processed successfully! Template placeholders have been replaced with test data.";
        $test_results = $results;

    } catch (Exception $e) {
        $error_message = 'Error: ' . $e->getMessage();
    }
}

// Get birthday templates
$templates = [];
try {
    $stmt = $pdo->prepare("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error_message = 'Error retrieving templates: ' . $e->getMessage();
}

// Get site settings
$sitename = get_site_setting('site_name', 'Church Management System');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Birthday Emails - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="<?php echo admin_url_for('admin-css-proxy.php'); ?>">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <div class="col main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-envelope-check me-2"></i>Test Birthday Emails</h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Instructions -->
                <div class="alert alert-info instruction-panel" role="alert">
                    <h5><i class="bi bi-info-circle me-2"></i>How to Test Birthday Emails</h5>
                    <ul class="mb-0">
                        <li>Select a birthday email template from the dropdown</li>
                        <li>Enter a test email address where you want to receive the test email</li>
                        <li>Provide a test name that will be used in the email</li>
                        <li>Click "Send Test Email" to send a sample birthday email</li>
                        <li>The test email will use today's date as the birthday for testing purposes</li>
                    </ul>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0"><i class="bi bi-envelope-paper me-2"></i>Send Test Birthday Email</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="template_id" class="form-label">Birthday Template *</label>
                                        <select class="form-select" id="template_id" name="template_id" required>
                                            <option value="">Select a template...</option>
                                            <?php foreach ($templates as $template): ?>
                                                <option value="<?php echo $template['id']; ?>" 
                                                        <?php echo (isset($_POST['template_id']) && $_POST['template_id'] == $template['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($template['template_name']); ?>
                                                    (<?php echo htmlspecialchars($template['subject']); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <?php if (empty($templates)): ?>
                                            <div class="form-text text-warning">
                                                <i class="bi bi-exclamation-triangle me-1"></i>
                                                No birthday templates found. <a href="email_templates.php">Create one first</a>.
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="test_email" class="form-label">Test Email Address *</label>
                                        <input type="email" class="form-control" id="test_email" name="test_email" 
                                               value="<?php echo htmlspecialchars($_POST['test_email'] ?? $admin_email); ?>" 
                                               placeholder="Enter email address to receive test" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="test_name" class="form-label">Test Name</label>
                                        <input type="text" class="form-control" id="test_name" name="test_name" 
                                               value="<?php echo htmlspecialchars($_POST['test_name'] ?? 'Test User'); ?>" 
                                               placeholder="Name to use in the test email">
                                    </div>

                                    <div class="mb-3">
                                        <label for="days_range" class="form-label">Days Range</label>
                                        <select class="form-select" id="days_range" name="days_range">
                                            <option value="0" <?php echo (isset($_POST['days_range']) && $_POST['days_range'] == 0) ? 'selected' : ''; ?>>
                                                Today (Birthday Email)
                                            </option>
                                            <option value="1" <?php echo (isset($_POST['days_range']) && $_POST['days_range'] == 1) ? 'selected' : ''; ?>>
                                                Tomorrow (1 Day Reminder)
                                            </option>
                                            <option value="3" <?php echo (isset($_POST['days_range']) && $_POST['days_range'] == 3) ? 'selected' : ''; ?>>
                                                3 Days Ahead
                                            </option>
                                            <option value="7" <?php echo (isset($_POST['days_range']) && $_POST['days_range'] == 7) ? 'selected' : ''; ?>>
                                                7 Days Ahead
                                            </option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-send me-2"></i>Send Test Email
                                    </button>
                                    <a href="send_birthday_emails.php" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-2"></i>Back to Birthday Emails
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0"><i class="bi bi-info-circle me-2"></i>Test Information</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Available Templates:</strong> <?php echo count($templates); ?></p>
                                <p><strong>Test Date:</strong> <?php echo date('F j, Y'); ?> (Today)</p>
                                <p><strong>Admin Email:</strong> <?php echo htmlspecialchars($admin_email); ?></p>
                                
                                <?php if (!empty($test_results)): ?>
                                    <hr>
                                    <h6>Last Test Results:</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Sent:</strong> <?php echo $test_results['total_sent'] ?? 0; ?></li>
                                        <li><strong>Failed:</strong> <?php echo $test_results['total_failed'] ?? 0; ?></li>
                                    </ul>

                                    <?php if (isset($_SESSION['last_test_subject'])): ?>
                                        <hr>
                                        <h6>Processed Template:</h6>
                                        <p><strong>Subject:</strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($_SESSION['last_test_subject']); ?></small></p>
                                        <p><strong>Content Preview:</strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($_SESSION['last_test_content']); ?></small></p>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0"><i class="bi bi-gear me-2"></i>Quick Actions</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="email_templates.php" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-envelope me-2"></i>Manage Templates
                                    </a>
                                    <a href="send_birthday_emails.php" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-send me-2"></i>Send Real Emails
                                    </a>
                                    <a href="email_analytics.php" class="btn btn-outline-info btn-sm">
                                        <i class="bi bi-graph-up me-2"></i>Email Analytics
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
