<?php
session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header('Location: login.php');
    exit();
}

$userId = $_SESSION['user_id'];

// Get event ID from URL
$eventId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if (!$eventId) {
    header('Location: events.php');
    exit();
}

try {
    // Get event details
    $stmt = $pdo->prepare("
        SELECT e.*, 
               COUNT(CASE WHEN er.status = 'attending' THEN 1 END) as attending_count,
               COUNT(CASE WHEN er.status = 'maybe' THEN 1 END) as maybe_count,
               COUNT(CASE WHEN er.status = 'not_attending' THEN 1 END) as not_attending_count
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.id = ? AND e.is_active = 1
        GROUP BY e.id
    ");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header('Location: events.php');
        exit();
    }
    
    // Get user's RSVP status
    $stmt = $pdo->prepare("SELECT status, notes FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $userId]);
    $userRsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get attendee list (only for attending users)
    $stmt = $pdo->prepare("
        SELECT m.first_name, m.last_name, er.status, er.created_at
        FROM event_rsvps er
        JOIN members m ON er.user_id = m.id
        WHERE er.event_id = ? AND er.status = 'attending'
        ORDER BY er.created_at ASC
    ");
    $stmt->execute([$eventId]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Check if event is in the future (event_date is already datetime)
    $isFutureEvent = strtotime($event['event_date']) > time();
    
} catch (PDOException $e) {
    error_log("Event detail error: " . $e->getMessage());
    header('Location: events.php');
    exit();
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$result = $stmt->fetch();
if ($result) {
    $sitename = $result['setting_value'];
}

// Get user data for navigation from members table
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$userId]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .navbar-nav .nav-link.active {
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }



        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
            width: 280px;
        }

        .sidebar-content {
            padding: 1.5rem;
        }

        .user-info {
            text-align: center;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #eee;
            margin-bottom: 1.5rem;
        }

        .user-avatar i {
            font-size: 3rem;
            color: #667eea;
        }

        .sidebar-nav .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }

        .sidebar-nav .nav-link:hover {
            background-color: #f8f9fa;
            color: #667eea;
            text-decoration: none;
        }

        .sidebar-nav .nav-link.active {
            background-color: #667eea;
            color: white;
        }

        .main-content {
            padding: 2rem;
        }

        .content-header {
            margin-bottom: 2rem;
        }

        .card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border-radius: 15px;
        }

        .event-card {
            background: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .event-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .attendee-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .attendee-item:last-child {
            border-bottom: none;
        }

        .attendee-avatar i {
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="user-details">
                        <h6><?php echo htmlspecialchars($_SESSION['user_name']); ?></h6>
                        <small class="text-muted">Member</small>
                    </div>
                </div>
                
                <nav class="sidebar-nav">
                    <a href="dashboard.php" class="nav-link">
                        <i class="bi bi-house"></i> Dashboard
                    </a>
                    <a href="events.php" class="nav-link active">
                        <i class="bi bi-calendar-event"></i> Events
                    </a>
                    <a href="directory.php" class="nav-link">
                        <i class="bi bi-people"></i> Directory
                    </a>
                    <a href="profile.php" class="nav-link">
                        <i class="bi bi-person"></i> Profile
                    </a>
                    <a href="settings.php" class="nav-link">
                        <i class="bi bi-gear"></i> Settings
                    </a>
                    <a href="logout.php" class="nav-link">
                        <i class="bi bi-box-arrow-right"></i> Logout
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-calendar-event"></i> Event Details</h1>
                        <p class="text-muted">View event information and manage your RSVP</p>
                    </div>
                    <a href="events.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Back to Events
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Event Information -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?php echo htmlspecialchars($event['title']); ?></h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6><i class="bi bi-calendar3"></i> Date & Time</h6>
                                    <p class="mb-0">
                                        <?php echo date('l, F j, Y', strtotime($event['event_date'])); ?><br>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($event['event_date'])); ?></small>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="bi bi-geo-alt"></i> Location</h6>
                                    <p class="mb-0"><?php echo htmlspecialchars($event['location']); ?></p>
                                </div>
                            </div>
                            
                            <?php if ($event['max_attendees']): ?>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6><i class="bi bi-people"></i> Capacity</h6>
                                    <p class="mb-0">
                                        <?php echo $event['attending_count']; ?> / <?php echo $event['max_attendees']; ?> attendees
                                        <div class="progress mt-1" style="height: 6px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: <?php echo ($event['attending_count'] / $event['max_attendees']) * 100; ?>%"></div>
                                        </div>
                                    </p>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <h6><i class="bi bi-info-circle"></i> Description</h6>
                                <p><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                            </div>
                            
                            <?php if (!empty($event['image_path'])): ?>
                            <div class="mb-3">
                                <h6><i class="bi bi-image"></i> Event Image</h6>
                                <img src="../<?php echo htmlspecialchars($event['image_path']); ?>"
                                     alt="Event Image" class="img-fluid rounded" style="max-height: 300px;">
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- RSVP and Attendees -->
                <div class="col-lg-4">
                    <!-- RSVP Section -->
                    <?php if ($isFutureEvent): ?>
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="bi bi-check-circle"></i> RSVP Status</h6>
                        </div>
                        <div class="card-body">
                            <?php if ($userRsvp): ?>
                                <div class="alert alert-info">
                                    <strong>Your RSVP:</strong> 
                                    <?php 
                                    $statusLabels = [
                                        'attending' => 'Attending',
                                        'maybe' => 'Maybe',
                                        'not_attending' => 'Not Attending'
                                    ];
                                    echo $statusLabels[$userRsvp['status']];
                                    ?>
                                    <?php if ($userRsvp['notes']): ?>
                                        <br><small><?php echo htmlspecialchars($userRsvp['notes']); ?></small>
                                    <?php endif; ?>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm" onclick="updateRsvp(<?php echo $eventId; ?>)">
                                    Update RSVP
                                </button>
                            <?php else: ?>
                                <p class="text-muted">You haven't RSVP'd to this event yet.</p>
                                <button type="button" class="btn btn-success" onclick="rsvpEvent(<?php echo $eventId; ?>)">
                                    RSVP Now
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="alert alert-secondary">
                                <i class="bi bi-clock-history"></i> This event has already occurred.
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Event Statistics -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="bi bi-bar-chart"></i> RSVP Summary</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-success"><?php echo $event['attending_count']; ?></div>
                                        <div class="stat-label">Attending</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-warning"><?php echo $event['maybe_count']; ?></div>
                                        <div class="stat-label">Maybe</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-item">
                                        <div class="stat-number text-danger"><?php echo $event['not_attending_count']; ?></div>
                                        <div class="stat-label">Not Attending</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendees List -->
                    <?php if (!empty($attendees)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0"><i class="bi bi-people"></i> Attendees (<?php echo count($attendees); ?>)</h6>
                        </div>
                        <div class="card-body">
                            <div class="attendees-list">
                                <?php foreach ($attendees as $attendee): ?>
                                <div class="attendee-item d-flex align-items-center mb-2">
                                    <div class="attendee-avatar me-2">
                                        <i class="bi bi-person-circle text-muted"></i>
                                    </div>
                                    <div class="attendee-info">
                                        <div class="attendee-name">
                                            <?php echo htmlspecialchars($attendee['first_name'] . ' ' . $attendee['last_name']); ?>
                                        </div>
                                        <small class="text-muted">
                                            RSVP'd <?php echo date('M j', strtotime($attendee['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- RSVP Modal -->
<div class="modal fade" id="rsvpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">RSVP for Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rsvpForm">
                    <input type="hidden" id="rsvp_event_id" name="event_id" value="<?php echo $eventId; ?>">

                    <div class="mb-3">
                        <label class="form-label">RSVP Status</label>
                        <div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="rsvp_response" id="attending" value="attending" 
                                       <?php echo ($userRsvp && $userRsvp['status'] === 'attending') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="attending">
                                    <i class="bi bi-check-circle text-success"></i> I will attend
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="rsvp_response" id="maybe" value="maybe"
                                       <?php echo ($userRsvp && $userRsvp['status'] === 'maybe') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maybe">
                                    <i class="bi bi-question-circle text-warning"></i> Maybe
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="rsvp_response" id="not_attending" value="not_attending"
                                       <?php echo ($userRsvp && $userRsvp['status'] === 'not_attending') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="not_attending">
                                    <i class="bi bi-x-circle text-danger"></i> Cannot attend
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rsvp_notes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="rsvp_notes" name="notes" rows="3" 
                                  placeholder="Any special requirements or comments..."><?php echo $userRsvp ? htmlspecialchars($userRsvp['notes']) : ''; ?></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
            </div>
        </div>
    </div>
</div>

<script>
    function rsvpEvent(eventId, defaultStatus = 'attending') {
        document.getElementById('rsvp_event_id').value = eventId;
        
        // Set default radio button if no current RSVP
        <?php if (!$userRsvp): ?>
        document.querySelector(`input[name="rsvp_response"][value="${defaultStatus}"]`).checked = true;
        <?php endif; ?>

        new bootstrap.Modal(document.getElementById('rsvpModal')).show();
    }

    function updateRsvp(eventId) {
        rsvpEvent(eventId);
    }

    function submitRSVP() {
        const form = document.getElementById('rsvpForm');
        const formData = new FormData(form);

        // Get selected response
        const selectedResponse = document.querySelector('input[name="rsvp_response"]:checked');
        if (!selectedResponse) {
            alert('Please select an RSVP status.');
            return;
        }
        
        formData.set('status', selectedResponse.value);
        formData.append('action', 'rsvp');

        fetch('rsvp_handler.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('RSVP updated successfully!');
                bootstrap.Modal.getInstance(document.getElementById('rsvpModal')).hide();
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }
</script>

<!-- Footer -->
<?php include 'includes/footer.php'; ?>
</body>
</html>
