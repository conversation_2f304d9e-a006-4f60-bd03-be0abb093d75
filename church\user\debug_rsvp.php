<?php
/**
 * Debug RSVP Database Structure
 */

require_once '../config.php';

try {
    echo "<h2>Event RSVP Table Structure</h2>";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'event_rsvps'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>ERROR: event_rsvps table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ event_rsvps table exists</p>";
    
    // Show table structure
    echo "<h3>Table Columns:</h3>";
    $stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Column List:</h3>";
    echo "<p>" . implode(', ', $columns) . "</p>";
    
    // Check for specific columns
    echo "<h3>Column Checks:</h3>";
    $check_columns = ['guest_count', 'guests_count', 'user_id', 'member_id', 'notes', 'status'];
    foreach ($check_columns as $col) {
        $exists = in_array($col, $columns);
        $status = $exists ? "✓ EXISTS" : "✗ MISSING";
        $color = $exists ? "green" : "red";
        echo "<p style='color: $color;'>$col: $status</p>";
    }
    
    // Show sample data
    echo "<h3>Sample Data (first 5 rows):</h3>";
    $stmt = $pdo->query("SELECT * FROM event_rsvps LIMIT 5");
    if ($stmt->rowCount() > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        $first = true;
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($first) {
                echo "<tr>";
                foreach (array_keys($row) as $header) {
                    echo "<th>" . htmlspecialchars($header) . "</th>";
                }
                echo "</tr>";
                $first = false;
            }
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No data in table</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
