<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

echo "<h2>Email Settings Synchronization Test</h2>";

// Test the email settings synchronization logic
if (isset($_POST['test_sync'])) {
    echo "<h3>Testing Email Settings Sync...</h3>";
    
    // Simulate the email settings data that would come from the form
    $testEmailData = [
        'smtp_host' => $_POST['smtp_host'] ?? 'test.smtp.com',
        'smtp_port' => $_POST['smtp_port'] ?? '587',
        'smtp_username' => $_POST['smtp_username'] ?? '<EMAIL>',
        'smtp_password' => $_POST['smtp_password'] ?? 'testpass',
        'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
        'from_email' => $_POST['from_email'] ?? '<EMAIL>',
        'from_name' => $_POST['from_name'] ?? 'Test Church',
        'reply_to_email' => $_POST['reply_to_email'] ?? '<EMAIL>'
    ];
    
    try {
        echo "<p><strong>Step 1:</strong> Creating email_settings table if it doesn't exist...</p>";
        
        // Ensure email_settings table exists with correct structure
        $pdo->exec("CREATE TABLE IF NOT EXISTS email_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        echo "<p style='color: green;'>✓ Table created/verified successfully</p>";
        
        echo "<p><strong>Step 2:</strong> Mapping settings data...</p>";
        
        // Map settings table keys to email_settings table keys
        $emailSettingsData = [];
        $emailKeys = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email'];

        foreach ($emailKeys as $key) {
            if (isset($testEmailData[$key])) {
                echo "<p>Processing: $key = " . htmlspecialchars($testEmailData[$key]) . "</p>";
                switch ($key) {
                    case 'smtp_encryption':
                        $emailSettingsData['smtp_secure'] = $testEmailData[$key];
                        break;
                    case 'from_email':
                        $emailSettingsData['sender_email'] = $testEmailData[$key];
                        break;
                    case 'from_name':
                        $emailSettingsData['sender_name'] = $testEmailData[$key];
                        break;
                    case 'reply_to_email':
                        $emailSettingsData['reply_to_email'] = $testEmailData[$key];
                        $emailSettingsData['replyToEmail'] = $testEmailData[$key]; // Also set the alternative key format
                        break;
                    default:
                        $emailSettingsData[$key] = $testEmailData[$key];
                        break;
                }
            }
        }

        // Add default smtp_auth
        $emailSettingsData['smtp_auth'] = '1';
        
        echo "<p><strong>Step 3:</strong> Final mapped data:</p>";
        echo "<pre>" . print_r($emailSettingsData, true) . "</pre>";
        
        echo "<p><strong>Step 4:</strong> Updating email_settings table...</p>";
        
        // Update email_settings table
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value, updated_at) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = VALUES(updated_at)");

        $successCount = 0;
        foreach ($emailSettingsData as $key => $value) {
            $result = $stmt->execute([$key, $value]);
            if ($result) {
                echo "<p style='color: green;'>✓ Updated: $key = " . htmlspecialchars($value) . "</p>";
                $successCount++;
            } else {
                echo "<p style='color: red;'>✗ Failed: $key = " . htmlspecialchars($value) . "</p>";
            }
        }

        echo "<p><strong>Result:</strong> Successfully updated $successCount out of " . count($emailSettingsData) . " settings</p>";
        
        // Also update the main settings table for comparison
        echo "<p><strong>Step 5:</strong> Updating main settings table...</p>";
        
        $settingsStmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        
        foreach ($testEmailData as $key => $value) {
            $result = $settingsStmt->execute([$key, $value]);
            if ($result) {
                echo "<p style='color: green;'>✓ Updated settings table: $key = " . htmlspecialchars($value) . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed settings table: $key = " . htmlspecialchars($value) . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    }
}

// Display current state
echo "<h3>Current Database State</h3>";

// Show settings table
echo "<h4>Settings Table (email-related):</h4>";
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '%smtp%' OR setting_key LIKE '%email%' OR setting_key LIKE '%from_%' ORDER BY setting_key");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($settings)) {
        echo "<p>No email-related settings found in settings table.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
        foreach ($settings as $setting) {
            echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($setting['setting_value']) . "</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error reading settings table: " . $e->getMessage() . "</p>";
}

// Show email_settings table
echo "<h4>Email Settings Table:</h4>";
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value, updated_at FROM email_settings ORDER BY setting_key");
    $stmt->execute();
    $emailSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($emailSettings)) {
        echo "<p>No settings found in email_settings table.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Setting Key</th><th>Setting Value</th><th>Updated At</th></tr>";
        foreach ($emailSettings as $setting) {
            echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($setting['setting_value']) . "</td><td>" . htmlspecialchars($setting['updated_at']) . "</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error reading email_settings table: " . $e->getMessage() . "</p>";
}

?>

<h3>Test Email Settings Synchronization</h3>
<form method="POST">
    <table>
        <tr><td>SMTP Host:</td><td><input type="text" name="smtp_host" value="smtp.hostinger.com"></td></tr>
        <tr><td>SMTP Port:</td><td><input type="text" name="smtp_port" value="465"></td></tr>
        <tr><td>SMTP Username:</td><td><input type="text" name="smtp_username" value="<EMAIL>"></td></tr>
        <tr><td>SMTP Password:</td><td><input type="text" name="smtp_password" value="testpassword"></td></tr>
        <tr><td>SMTP Encryption:</td><td>
            <select name="smtp_encryption">
                <option value="tls">TLS</option>
                <option value="ssl" selected>SSL</option>
                <option value="none">None</option>
            </select>
        </td></tr>
        <tr><td>From Email:</td><td><input type="text" name="from_email" value="<EMAIL>"></td></tr>
        <tr><td>From Name:</td><td><input type="text" name="from_name" value="Freedom Assembly Church"></td></tr>
        <tr><td>Reply To Email:</td><td><input type="text" name="reply_to_email" value="<EMAIL>"></td></tr>
    </table>
    <p><input type="submit" name="test_sync" value="Test Synchronization"></p>
</form>

<p><a href="settings.php">Go to Settings Page</a> | <a href="email_settings.php">Go to Email Settings Page</a></p>
