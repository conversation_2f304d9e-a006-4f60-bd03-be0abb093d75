<?php
session_start();
require_once 'config.php';

// Get payment settings
try {
    $stmt = $pdo->prepare("SELECT * FROM payment_settings");
    $stmt->execute();
    $payment_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $payment_settings[$row['setting_key']] = $row['setting_value'];
    }
    
    // Set default values if settings are missing
    if (!isset($payment_settings['paypal_enabled'])) {
        $payment_settings['paypal_enabled'] = '1'; // Enable PayPal by default
    }
    if (!isset($payment_settings['stripe_enabled'])) {
        $payment_settings['stripe_enabled'] = '0'; // Disable Stripe by default
    }
    if (!isset($payment_settings['donations_enabled'])) {
        $payment_settings['donations_enabled'] = '1'; // Enable donations by default
    }
} catch (PDOException $e) {
    error_log("Error loading payment settings: " . $e->getMessage());
    die("Error processing donation. Please try again later.");
}

// Validate donation is enabled
if ($payment_settings['donations_enabled'] !== '1') {
    die("Online donations are currently disabled.");
}

// Validate input
$required_fields = ['donor_name', 'donor_email', 'amount', 'currency', 'payment_method'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        die("Missing required field: $field");
    }
}

// Sanitize and validate input
$donor_name = htmlspecialchars($_POST['donor_name'], ENT_QUOTES, 'UTF-8');
$donor_email = filter_var($_POST['donor_email'], FILTER_SANITIZE_EMAIL);
if (!filter_var($donor_email, FILTER_VALIDATE_EMAIL)) {
    die("Invalid email address");
}

$amount = filter_var($_POST['amount'], FILTER_VALIDATE_FLOAT);
if ($amount < floatval($payment_settings['minimum_donation_amount'])) {
    die("Amount is below minimum donation amount");
}

$currency = htmlspecialchars($_POST['currency'], ENT_QUOTES, 'UTF-8');
$payment_method = htmlspecialchars($_POST['payment_method'], ENT_QUOTES, 'UTF-8');
$message = isset($_POST['message']) ? htmlspecialchars($_POST['message'], ENT_QUOTES, 'UTF-8') : '';

// Validate donation type and recipient
$donation_type = htmlspecialchars($_POST['donation_type'] ?? 'general', ENT_QUOTES, 'UTF-8');
$recipient_id = null;
if ($donation_type === 'birthday_gift') {
    if (!isset($_POST['recipient_id']) || empty($_POST['recipient_id'])) {
        die("Recipient is required for birthday gifts");
    }
    $recipient_id = filter_var($_POST['recipient_id'], FILTER_VALIDATE_INT);
    
    // Verify recipient exists and has an upcoming birthday
    try {
        $stmt = $pdo->prepare("SELECT id FROM members WHERE id = ? AND birth_date IS NOT NULL AND (
            (MONTH(birth_date) = MONTH(CURRENT_DATE) AND DAY(birth_date) >= DAY(CURRENT_DATE))
            OR 
            (MONTH(birth_date) = MONTH(DATE_ADD(CURRENT_DATE, INTERVAL 1 MONTH)) 
             AND DAY(birth_date) <= DAY(DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)))
        )");
        $stmt->execute([$recipient_id]);
        if (!$stmt->fetch()) {
            die("Invalid recipient or no upcoming birthday");
        }
    } catch (PDOException $e) {
        error_log("Error validating recipient: " . $e->getMessage());
        die("Error processing donation. Please try again later.");
    }
}

// Start transaction
try {
    $pdo->beginTransaction();
    
    // Create donation record
    $stmt = $pdo->prepare("INSERT INTO donations (
        donor_name, donor_email, amount, currency, payment_method,
        donation_type, recipient_id, message, payment_status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
    
    $stmt->execute([
        $donor_name, $donor_email, $amount, $currency, $payment_method,
        $donation_type, $recipient_id, $message
    ]);
    
    $donation_id = $pdo->lastInsertId();
    
    // Process payment based on method
    if ($payment_method === 'paypal') {
        // Check if the PayPal SDK is installed
        if (!file_exists('vendor/autoload.php')) {
            error_log("PayPal SDK not found. Please run 'composer require paypal/paypal-checkout-sdk' to install it.");
            die("PayPal SDK not installed. Please contact the administrator.");
        }
        
        require_once 'vendor/autoload.php';
        
        // Check if the PayPal SDK classes exist
        if (!class_exists('\\PayPalCheckoutSdk\\Core\\SandboxEnvironment')) {
            error_log("PayPal SDK classes not found. Please run 'composer require paypal/paypal-checkout-sdk' to install the correct version.");
            die("PayPal SDK not properly installed. Please contact the administrator.");
        }
        
        // Initialize PayPal
        $client_id = $payment_settings['paypal_client_id'];
        $client_secret = $payment_settings['paypal_client_secret'];
        $is_sandbox = $payment_settings['paypal_sandbox_mode'] === '1';
        
        $environment = $is_sandbox ? 
            new \PayPalCheckoutSdk\Core\SandboxEnvironment($client_id, $client_secret) :
            new \PayPalCheckoutSdk\Core\ProductionEnvironment($client_id, $client_secret);
        
        $client = new \PayPalCheckoutSdk\Core\PayPalHttpClient($environment);
        
        // Create order
        $request = new \PayPalCheckoutSdk\Orders\OrdersCreateRequest();
        $request->prefer('return=representation');
        
        $request->body = [
            'intent' => 'CAPTURE',
            'purchase_units' => [[
                'reference_id' => $donation_id,
                'amount' => [
                    'currency_code' => $currency,
                    'value' => number_format($amount, 2, '.', '')
                ],
                'description' => $donation_type === 'birthday_gift' ? 'Birthday Gift Donation' : 'Church Donation'
            ]],
            'application_context' => [
                'return_url' => SITE_URL . '/complete_paypal_payment.php',
                'cancel_url' => SITE_URL . '/cancel_payment.php'
            ]
        ];
        
        try {
            $response = $client->execute($request);
            
            // Store transaction details
            $stmt = $pdo->prepare("INSERT INTO payment_transactions (
                donation_id, payment_provider, provider_transaction_id,
                amount, currency, payment_status, payment_method_details
            ) VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            $stmt->execute([
                $donation_id,
                'paypal',
                $response->result->id,
                $amount,
                $currency,
                'pending',
                json_encode($response->result)
            ]);
            
            $pdo->commit();
            
            // Redirect to PayPal
            foreach ($response->result->links as $link) {
                if ($link->rel === 'approve') {
                    header('Location: ' . $link->href);
                    exit();
                }
            }
            
        } catch (Exception $e) {
            $pdo->rollBack();
            error_log("PayPal Error: " . $e->getMessage());
            die("Error processing PayPal payment. Please try again later.");
        }
        
    } elseif ($payment_method === 'stripe') {
        // Check if vendor/autoload.php exists
        if (!file_exists('vendor/autoload.php')) {
            error_log("Stripe Error: vendor/autoload.php not found. Composer dependencies may not be installed.");
            die("Payment processing error: Stripe SDK not installed. Please contact the administrator.");
        }
        
        require_once 'vendor/autoload.php';
        
        // Check if Stripe SDK is installed
        if (!class_exists('\Stripe\Stripe')) {
            error_log("Stripe Error: Stripe SDK not found. Make sure it's installed via Composer.");
            die("Payment processing error: Stripe SDK not installed. Please contact the administrator.");
        }
        
        // Initialize Stripe
        $stripe_secret_key = $payment_settings['stripe_secret_key'];
        \Stripe\Stripe::setApiKey($stripe_secret_key);
        
        try {
            // Create payment intent
            $payment_intent = \Stripe\PaymentIntent::create([
                'amount' => $amount * 100, // Convert to cents
                'currency' => strtolower($currency),
                'payment_method_types' => ['card'],
                'metadata' => [
                    'donation_id' => $donation_id,
                    'donation_type' => $donation_type,
                    'donor_name' => $donor_name,
                    'donor_email' => $donor_email
                ]
            ]);
            
            // Store transaction details
            $stmt = $pdo->prepare("INSERT INTO payment_transactions (
                donation_id, payment_provider, provider_transaction_id,
                amount, currency, payment_status, payment_method_details
            ) VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            $stmt->execute([
                $donation_id,
                'stripe',
                $payment_intent->id,
                $amount,
                $currency,
                'pending',
                json_encode($payment_intent)
            ]);
            
            $pdo->commit();
            
            // Return client secret for Stripe.js
            header('Content-Type: application/json');
            echo json_encode(['client_secret' => $payment_intent->client_secret]);
            exit();
            
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log("Stripe Error: " . $e->getMessage());
            http_response_code(500);
            echo json_encode(['error' => 'Error processing Stripe payment. Please try again later.']);
            exit();
        }
        
    } elseif ($payment_method === 'manual') {
        // Manual processing - just record the donation as pending
        $stmt = $pdo->prepare("INSERT INTO payment_transactions (
            donation_id, payment_provider, provider_transaction_id,
            amount, currency, payment_status, payment_method_details
        ) VALUES (?, ?, ?, ?, ?, ?, ?)");

        $stmt->execute([
            $donation_id,
            'manual',
            'MANUAL_' . $donation_id . '_' . time(),
            $amount,
            $currency,
            'pending',
            json_encode(['method' => 'manual', 'note' => 'Manual processing required'])
        ]);

        $pdo->commit();

        // Redirect to success page
        header("Location: donation_success.php?id=" . $donation_id);
        exit;

    } else {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        die("Invalid payment method");
    }
    
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Database Error: " . $e->getMessage());
    die("Error processing donation. Please try again later.");
}
?> 