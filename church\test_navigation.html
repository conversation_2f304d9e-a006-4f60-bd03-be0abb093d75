<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; }
        .test-link:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>Navigation Test Page</h1>
    <p>Click these links to test if the navigation routing is working correctly:</p>
    
    <a href="requests.php" class="test-link">Test Requests Page</a>
    <a href="dashboard.php" class="test-link">Test Dashboard Page</a>
    <a href="profile.php" class="test-link">Test Profile Page</a>
    <a href="settings.php" class="test-link">Test Settings Page</a>
    <a href="events.php" class="test-link">Test Events Page</a>
    <a href="birthday_templates.php" class="test-link">Test Birthday Templates Page</a>
    <a href="family_management.php" class="test-link">Test Family Management Page</a>
    <h3>Gifts Menu</h3>
    <a href="send_gift.php" class="test-link">Test Send Gift Page</a>
    <a href="my_gifts.php" class="test-link">Test My Gifts Page</a>
    <a href="change_password.php" class="test-link">Test Change Password Page</a>
    <a href="skills.php" class="test-link">Test Skills Page</a>
    <a href="volunteer_opportunities.php" class="test-link">Test Volunteer Opportunities Page</a>
    <a href="logout.php" class="test-link">Test Logout Page</a>
    <a href="login.php" class="test-link">Test Login Page</a>
    <a href="forgot_password.php" class="test-link">Test Forgot Password Page</a>
    <a href="birthday_gifts.php" class="test-link">Test Birthday Gifts Page</a>
    <a href="event_detail.php" class="test-link">Test Event Detail Page</a>
    <a href="enhanced_donate.php" class="test-link">Test Donation Page</a>

    <hr>
    <p><strong>Expected behavior:</strong> All links above should redirect to their respective pages in the user directory and show the actual page content, NOT the register.php content.</p>
    <p><strong>If you see register.php content:</strong> The routing fix didn't work properly.</p>
    <p><strong>If you see the actual page content:</strong> The routing fix is working correctly!</p>
</body>
</html>
