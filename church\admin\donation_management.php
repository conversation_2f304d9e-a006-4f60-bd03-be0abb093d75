<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Set page variables
$page_title = __('donation_management_system');
$page_header = __('donation_management_system');
$page_description = __('manage_donations_and_giving_options');

// Include header
include 'includes/header.php';
?>

<!-- Donation Management System Admin Interface -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-heart-fill text-primary"></i> Donation Management System
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Access the donation system to create donations, send birthday gifts, or view the public donation form.
                </p>
                
                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <i class="bi bi-cash-coin fs-1 text-primary mb-3"></i>
                                <h5 class="card-title">Manage Donations</h5>
                                <p class="card-text text-muted">View and manage all donations, update statuses, and track giving.</p>
                                <a href="donations.php" class="btn btn-primary">
                                    <i class="bi bi-arrow-right"></i> Go to Donations
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="bi bi-gift-fill fs-1 text-success mb-3"></i>
                                <h5 class="card-title">Gift Management</h5>
                                <p class="card-text text-muted">Manage birthday gifts, templates, and special occasion giving.</p>
                                <a href="gift_management.php" class="btn btn-success">
                                    <i class="bi bi-arrow-right"></i> Manage Gifts
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-info">
                            <div class="card-body text-center">
                                <i class="bi bi-box-arrow-up-right fs-1 text-info mb-3"></i>
                                <h5 class="card-title">Public Donation Form</h5>
                                <p class="card-text text-muted">View the public-facing donation form that members and visitors use.</p>
                                <a href="../donate.php" target="_blank" class="btn btn-info">
                                    <i class="bi bi-external-link"></i> View Public Form
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="bi bi-gear-fill fs-1 text-warning mb-3"></i>
                                <h5 class="card-title">Donation Settings</h5>
                                <p class="card-text text-muted">Configure payment methods, currencies, and donation options.</p>
                                <a href="setup_enhanced_donations.php" class="btn btn-warning">
                                    <i class="bi bi-gear"></i> Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-cash-stack fs-1 text-primary mb-2"></i>
                <h4 class="text-primary">
                    <?php
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM donations WHERE payment_status = 'completed'");
                    $stmt->execute();
                    echo number_format($stmt->fetchColumn());
                    ?>
                </h4>
                <p class="text-muted mb-0">Total Donations</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-currency-dollar fs-1 text-success mb-2"></i>
                <h4 class="text-success">
                    <?php
                    $stmt = $pdo->prepare("SELECT COALESCE(SUM(amount), 0) FROM donations WHERE payment_status = 'completed'");
                    $stmt->execute();
                    echo '$' . number_format($stmt->fetchColumn(), 2);
                    ?>
                </h4>
                <p class="text-muted mb-0">Total Amount</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-gift fs-1 text-warning mb-2"></i>
                <h4 class="text-warning">
                    <?php
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM donations WHERE donation_type = 'birthday_gift' AND payment_status = 'completed'");
                    $stmt->execute();
                    echo number_format($stmt->fetchColumn());
                    ?>
                </h4>
                <p class="text-muted mb-0">Birthday Gifts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-calendar-month fs-1 text-info mb-2"></i>
                <h4 class="text-info">
                    <?php
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM donations WHERE payment_status = 'completed' AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
                    $stmt->execute();
                    echo number_format($stmt->fetchColumn());
                    ?>
                </h4>
                <p class="text-muted mb-0">This Month</p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Donation Activity</h5>
                <a href="donations.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php
                $stmt = $pdo->prepare("
                    SELECT d.*, m.full_name as recipient_name
                    FROM donations d
                    LEFT JOIN members m ON d.recipient_id = m.id
                    ORDER BY d.created_at DESC
                    LIMIT 5
                ");
                $stmt->execute();
                $recent_donations = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (empty($recent_donations)): ?>
                    <p class="text-muted text-center py-4">No recent donation activity.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Donor</th>
                                    <th>Type</th>
                                    <th>Recipient</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_donations as $donation): ?>
                                <tr>
                                    <td>
                                        <?php echo htmlspecialchars($donation['donor_name'] ?: 'Anonymous'); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $donation['donation_type'] === 'birthday_gift' ? 'warning' : 'primary'; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $donation['donation_type'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        if ($donation['donation_type'] === 'birthday_gift' && $donation['recipient_name']) {
                                            echo htmlspecialchars($donation['recipient_name']);
                                        } else {
                                            echo '<span class="text-muted">General Fund</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($donation['currency'] . ' ' . number_format($donation['amount'], 2)); ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($donation['payment_status']) {
                                            case 'completed':
                                                $status_class = 'success';
                                                break;
                                            case 'pending':
                                                $status_class = 'warning';
                                                break;
                                            case 'failed':
                                                $status_class = 'danger';
                                                break;
                                            default:
                                                $status_class = 'secondary';
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo ucfirst($donation['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y g:i A', strtotime($donation['created_at'])); ?>
                                        </small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
