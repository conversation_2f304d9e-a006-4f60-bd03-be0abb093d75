<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

echo "<h1>Forgot Password Email Diagnostic</h1>";

// Test email settings
echo "<h2>1. Email Settings Check</h2>";
echo "<h3>Global Email Settings:</h3>";
echo "<pre>" . print_r($emailSettings, true) . "</pre>";

// Check if all required settings are present
$required_settings = ['smtp_host', 'smtp_auth', 'smtp_username', 'smtp_password', 'smtp_secure', 'smtp_port', 'sender_email', 'sender_name'];
$missing_settings = array_diff($required_settings, array_keys($emailSettings));

if (!empty($missing_settings)) {
    echo "<p style='color: red;'><strong>Missing required email settings:</strong> " . implode(', ', $missing_settings) . "</p>";
} else {
    echo "<p style='color: green;'><strong>✓ All required email settings are present</strong></p>";
}

// Test database connection and admin table
echo "<h2>2. Admin Table Check</h2>";
try {
    $stmt = $pdo->prepare("SELECT id, username, email, full_name FROM admins LIMIT 5");
    $stmt->execute();
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✓ Admin table accessible</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th></tr>";
    foreach ($admins as $admin) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($admin['id']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error accessing admin table: " . $e->getMessage() . "</p>";
}

// Test email template
echo "<h2>3. Email Template Check</h2>";
try {
    $stmt = $pdo->prepare("SELECT subject, content FROM email_templates WHERE template_name LIKE '%password%reset%' LIMIT 1");
    $stmt->execute();
    $template = $stmt->fetch();
    
    if ($template) {
        echo "<p style='color: green;'>✓ Password reset email template found</p>";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        echo "<p><strong>Content Preview:</strong></p>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($template['content'], 0, 500)) . "...";
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠ No password reset email template found - will use default template</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error checking email templates: " . $e->getMessage() . "</p>";
}

// Test sendEmail function
echo "<h2>4. Email Function Test</h2>";

if (isset($_POST['test_email'])) {
    $test_email = $_POST['test_email'];
    $test_name = $_POST['test_name'] ?? 'Test Admin';
    
    echo "<h3>Testing email to: $test_name &lt;$test_email&gt;</h3>";
    
    // Create a test reset URL
    $test_token = 'test_token_' . time();
    $test_reset_url = ADMIN_URL . "/reset_password.php?token=" . urlencode($test_token);
    
    // Test the sendPasswordResetEmail function
    $test_admin = [
        'email' => $test_email,
        'full_name' => $test_name
    ];
    
    // Create the sendPasswordResetEmail function inline for testing
    function testSendPasswordResetEmail($admin, $resetUrl) {
        global $pdo;
        
        echo "<p><strong>Step 1:</strong> Getting email template...</p>";
        
        // Get email template
        $stmt = $pdo->prepare("SELECT subject, content FROM email_templates WHERE template_name LIKE '%password%reset%' LIMIT 1");
        $stmt->execute();
        $template = $stmt->fetch();
        
        if (!$template) {
            echo "<p>Using default template</p>";
            // Use default template if none found
            $subject = "Password Reset - " . get_organization_name() . " Admin";
            $content = "
                <html>
                <body>
                    <h2>Password Reset Request</h2>
                    <p>Dear {full_name},</p>
                    <p>We received a request to reset your password for the " . htmlspecialchars(get_organization_name()) . " Admin Portal.</p>
                    <p>To reset your password, please click the link below:</p>
                    <p><a href='{reset_url}'>Reset Password</a></p>
                    <p>This link will expire in 4 hours.</p>
                    <p>If you did not request a password reset, please ignore this email or contact an administrator.</p>
                    <p>Thank you,<br>" . htmlspecialchars(get_organization_name()) . " Admin Team</p>
                </body>
                </html>
            ";
        } else {
            echo "<p>Using database template</p>";
            $subject = $template['subject'];
            $content = $template['content'];
        }
        
        echo "<p><strong>Step 2:</strong> Replacing placeholders...</p>";
        
        // Replace placeholders
        $subject = str_replace('{full_name}', $admin['full_name'], $subject);
        
        // Make sure the reset URL is properly encoded for HTML
        $safeResetUrl = htmlspecialchars($resetUrl, ENT_QUOTES, 'UTF-8');
        
        $content = str_replace(
            ['{full_name}', '{reset_url}', '{expires}'],
            [$admin['full_name'], $safeResetUrl, '4 hours'],
            $content
        );
        
        echo "<p><strong>Final Subject:</strong> " . htmlspecialchars($subject) . "</p>";
        echo "<p><strong>Final Content Preview:</strong></p>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($content, 0, 800)) . "...";
        echo "</div>";
        
        echo "<p><strong>Step 3:</strong> Calling sendEmail function...</p>";
        
        // Send email using the global function
        $result = sendEmail(
            $admin['email'],
            $admin['full_name'],
            $subject,
            $content,
            true
        );
        
        return $result;
    }
    
    $emailSent = testSendPasswordResetEmail($test_admin, $test_reset_url);
    
    if ($emailSent) {
        echo "<p style='color: green; font-weight: bold;'>✓ Email sent successfully!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ Email sending failed!</p>";
        
        // Check for last email error
        global $last_email_error;
        if ($last_email_error) {
            echo "<p><strong>Error details:</strong> " . htmlspecialchars($last_email_error) . "</p>";
        }
        
        // Check email debug log
        $debug_log_file = __DIR__ . '/../logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            echo "<p><strong>Recent email debug log:</strong></p>";
            $log_content = file_get_contents($debug_log_file);
            $log_lines = explode("\n", $log_content);
            $recent_lines = array_slice($log_lines, -20); // Last 20 lines
            echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars(implode("\n", $recent_lines));
            echo "</pre>";
        }
    }
}

?>

<h2>5. Test Email Sending</h2>
<form method="POST">
    <p>
        <label>Test Email Address:</label><br>
        <input type="email" name="test_email" value="<?php echo htmlspecialchars($_POST['test_email'] ?? ''); ?>" required style="width: 300px;">
    </p>
    <p>
        <label>Test Name:</label><br>
        <input type="text" name="test_name" value="<?php echo htmlspecialchars($_POST['test_name'] ?? 'Test Admin'); ?>" required style="width: 300px;">
    </p>
    <p>
        <input type="submit" value="Send Test Password Reset Email" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
    </p>
</form>

<h2>6. Quick Fixes</h2>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
    <p><strong>If emails are not being sent, try these steps:</strong></p>
    <ol>
        <li><strong>Check Email Settings:</strong> Go to <a href="settings.php">Admin Settings</a> → Email Config tab and verify SMTP settings</li>
        <li><strong>Test Email Settings:</strong> Use the <a href="email_settings.php">Email Settings</a> page to test SMTP configuration</li>
        <li><strong>Check Email Logs:</strong> Look at the email debug log above for specific error messages</li>
        <li><strong>Verify Email Sync:</strong> Use the <a href="diagnostic.php">Diagnostic Page</a> to check email settings synchronization</li>
    </ol>
</div>

<p><a href="forgot_password.php">← Back to Forgot Password</a> | <a href="settings.php">Go to Settings</a> | <a href="email_settings.php">Go to Email Settings</a></p>
