<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

echo "<h1>Event Visibility Diagnostic & Fix</h1>";

$message = '';
$error = '';

// Fix hidden events
if (isset($_POST['fix_events'])) {
    try {
        // Get all events that should be visible but are marked as inactive
        $stmt = $pdo->prepare("
            SELECT id, title, event_date, is_active, created_at 
            FROM events 
            WHERE is_active = 0 
            AND event_date > NOW() 
            ORDER BY event_date ASC
        ");
        $stmt->execute();
        $hiddenEvents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($hiddenEvents)) {
            $message = "No hidden future events found that need fixing.";
        } else {
            // Ask for confirmation or auto-fix based on POST parameter
            if (isset($_POST['confirm_fix'])) {
                // Update all hidden future events to be active
                $updateStmt = $pdo->prepare("
                    UPDATE events 
                    SET is_active = 1 
                    WHERE is_active = 0 
                    AND event_date > NOW()
                ");
                $result = $updateStmt->execute();
                
                if ($result) {
                    $count = $updateStmt->rowCount();
                    $message = "Successfully made $count hidden events visible to users!";
                } else {
                    $error = "Failed to update events.";
                }
            } else {
                // Show events that would be fixed
                echo "<h2>Hidden Future Events Found</h2>";
                echo "<p style='color: orange;'>The following " . count($hiddenEvents) . " future events are currently hidden from users:</p>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Title</th><th>Event Date</th><th>Created</th><th>Status</th></tr>";
                foreach ($hiddenEvents as $event) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($event['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($event['title']) . "</td>";
                    echo "<td>" . htmlspecialchars($event['event_date']) . "</td>";
                    echo "<td>" . htmlspecialchars($event['created_at']) . "</td>";
                    echo "<td style='color: red;'>Hidden (is_active = 0)</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo "<form method='POST' style='margin: 20px 0;'>";
                echo "<input type='hidden' name='fix_events' value='1'>";
                echo "<input type='hidden' name='confirm_fix' value='1'>";
                echo "<p><strong>Do you want to make all these events visible to users?</strong></p>";
                echo "<input type='submit' value='Yes, Make All Events Visible' style='padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; margin-right: 10px;'>";
                echo "</form>";
                return; // Don't continue with the rest of the page
            }
        }
    } catch (Exception $e) {
        $error = "Error fixing events: " . $e->getMessage();
    }
}

// Display current event status
echo "<h2>Current Event Status</h2>";

try {
    // Get event statistics
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_events,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as visible_events,
            SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as hidden_events,
            SUM(CASE WHEN is_active = 1 AND event_date > NOW() THEN 1 ELSE 0 END) as visible_future_events,
            SUM(CASE WHEN is_active = 0 AND event_date > NOW() THEN 1 ELSE 0 END) as hidden_future_events,
            SUM(CASE WHEN event_date > NOW() THEN 1 ELSE 0 END) as total_future_events
        FROM events
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h3 style='margin: 0; color: #1976d2;'>" . $stats['total_events'] . "</h3>";
    echo "<p style='margin: 5px 0 0 0;'>Total Events</p>";
    echo "</div>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h3 style='margin: 0; color: #388e3c;'>" . $stats['visible_events'] . "</h3>";
    echo "<p style='margin: 5px 0 0 0;'>Visible Events</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h3 style='margin: 0; color: #f57c00;'>" . $stats['hidden_events'] . "</h3>";
    echo "<p style='margin: 5px 0 0 0;'>Hidden Events</p>";
    echo "</div>";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h3 style='margin: 0; color: #388e3c;'>" . $stats['visible_future_events'] . "</h3>";
    echo "<p style='margin: 5px 0 0 0;'>Visible Future Events</p>";
    echo "</div>";
    
    if ($stats['hidden_future_events'] > 0) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 8px; text-align: center;'>";
        echo "<h3 style='margin: 0; color: #d32f2f;'>" . $stats['hidden_future_events'] . "</h3>";
        echo "<p style='margin: 5px 0 0 0;'>Hidden Future Events ⚠️</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    if ($stats['hidden_future_events'] > 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ Issue Found!</h4>";
        echo "<p style='color: #856404; margin-bottom: 0;'>You have <strong>" . $stats['hidden_future_events'] . " future events</strong> that are hidden from users. These events were likely created with 'Draft' status instead of 'Published' status.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting event statistics: " . $e->getMessage() . "</p>";
}

// Show recent events
echo "<h2>Recent Events (Last 10)</h2>";
try {
    $stmt = $pdo->prepare("
        SELECT id, title, event_date, is_active, created_at,
               CASE WHEN is_active = 1 THEN 'Visible' ELSE 'Hidden' END as visibility_status
        FROM events 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $recentEvents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($recentEvents)) {
        echo "<p>No events found.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Event Date</th><th>Created</th><th>Status</th></tr>";
        foreach ($recentEvents as $event) {
            $rowStyle = $event['is_active'] == 0 ? "background-color: #ffebee;" : "";
            echo "<tr style='$rowStyle'>";
            echo "<td>" . htmlspecialchars($event['id']) . "</td>";
            echo "<td>" . htmlspecialchars($event['title']) . "</td>";
            echo "<td>" . htmlspecialchars($event['event_date']) . "</td>";
            echo "<td>" . htmlspecialchars($event['created_at']) . "</td>";
            $statusColor = $event['is_active'] == 1 ? 'green' : 'red';
            echo "<td style='color: $statusColor; font-weight: bold;'>" . $event['visibility_status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><small>Red rows indicate events that are hidden from users.</small></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting recent events: " . $e->getMessage() . "</p>";
}

?>

<?php if ($message): ?>
    <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>✓ Success:</strong> <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <strong>✗ Error:</strong> <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<h2>Actions</h2>

<form method="POST" style="margin: 20px 0;">
    <h3>Fix Hidden Events</h3>
    <p>This will check for future events that are currently hidden from users and give you the option to make them visible.</p>
    <input type="submit" name="fix_events" value="Check & Fix Hidden Events" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
</form>

<h2>How Event Visibility Works</h2>
<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h4>Event Status Meanings:</h4>
    <ul>
        <li><strong>Published:</strong> Event is visible to users on all pages (events page, dashboard, index page)</li>
        <li><strong>Draft:</strong> Event is hidden from users (only visible in admin panel)</li>
        <li><strong>Cancelled:</strong> Event is hidden from users</li>
        <li><strong>Completed:</strong> Event is hidden from users</li>
    </ul>
    
    <h4>To Make Events Visible:</h4>
    <ol>
        <li>Go to <a href="events.php">Admin Events Page</a></li>
        <li>Click "Edit" on the event you want to make visible</li>
        <li>Change the Status to "Published"</li>
        <li>Click "Update Event"</li>
    </ol>
    
    <p><strong>Note:</strong> The default status for new events has been changed to "Published" to prevent this issue in the future.</p>
</div>

<p><a href="events.php">← Back to Events</a></p>
