<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

echo "<h2>Email Settings Debug Information</h2>";

// Check current settings in settings table
echo "<h3>Current Settings in 'settings' table:</h3>";
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '%smtp%' OR setting_key LIKE '%email%' OR setting_key LIKE '%from_%' ORDER BY setting_key");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
    foreach ($settings as $setting) {
        echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($setting['setting_value']) . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error reading settings table: " . $e->getMessage() . "</p>";
}

// Check current settings in email_settings table
echo "<h3>Current Settings in 'email_settings' table:</h3>";
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value, updated_at FROM email_settings ORDER BY setting_key");
    $stmt->execute();
    $emailSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($emailSettings)) {
        echo "<p style='color: orange;'>No settings found in email_settings table.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Setting Key</th><th>Setting Value</th><th>Updated At</th></tr>";
        foreach ($emailSettings as $setting) {
            echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($setting['setting_value']) . "</td><td>" . htmlspecialchars($setting['updated_at']) . "</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error reading email_settings table: " . $e->getMessage() . "</p>";
}

// Check if email_settings table exists and its structure
echo "<h3>Email Settings Table Structure:</h3>";
try {
    $stmt = $pdo->prepare("DESCRIBE email_settings");
    $stmt->execute();
    $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking email_settings table structure: " . $e->getMessage() . "</p>";
}

// Test form submission simulation
echo "<h3>Test Form Submission:</h3>";
echo "<form method='POST' action='settings.php'>";
echo "<input type='hidden' name='email' value='1'>";
echo "<p>SMTP Host: <input type='text' name='smtp_host' value='test.smtp.com'></p>";
echo "<p>SMTP Port: <input type='text' name='smtp_port' value='587'></p>";
echo "<p>SMTP Username: <input type='text' name='smtp_username' value='<EMAIL>'></p>";
echo "<p>SMTP Password: <input type='text' name='smtp_password' value='testpass'></p>";
echo "<p>SMTP Encryption: <select name='smtp_encryption'><option value='tls'>TLS</option><option value='ssl'>SSL</option></select></p>";
echo "<p>From Email: <input type='text' name='from_email' value='<EMAIL>'></p>";
echo "<p>From Name: <input type='text' name='from_name' value='Test Church'></p>";
echo "<p>Reply To Email: <input type='text' name='reply_to_email' value='<EMAIL>'></p>";
echo "<p><input type='submit' value='Test Submit to settings.php'></p>";
echo "</form>";

echo "<p><a href='settings.php'>Go back to Settings</a></p>";
?>
