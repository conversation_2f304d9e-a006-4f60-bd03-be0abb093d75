<?php
/**
 * Birthday Gifts System
 * 
 * Allows users to send birthday gifts to other members
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Get upcoming birthdays (next 30 days)
try {
    $stmt = $pdo->prepare("
        SELECT 
            m.id,
            m.full_name,
            m.first_name,
            m.email,
            m.birth_date,
            m.image_path,
            DATEDIFF(
                DATE_ADD(
                    MAKEDATE(YEAR(CURDATE()), 1),
                    INTERVAL (DAYOFYEAR(m.birth_date) - 1) DAY
                ),
                CURDATE()
            ) as days_until_birthday,
            CASE 
                WHEN MONTH(m.birth_date) = MONTH(CURDATE()) AND DAY(m.birth_date) = DAY(CURDATE()) THEN 'today'
                WHEN MONTH(m.birth_date) = MONTH(CURDATE()) AND DAY(m.birth_date) > DAY(CURDATE()) THEN 'this_month'
                WHEN MONTH(m.birth_date) = MONTH(DATE_ADD(CURDATE(), INTERVAL 1 MONTH)) THEN 'next_month'
                ELSE 'later'
            END as birthday_period
        FROM members m
        WHERE m.status = 'active' 
        AND m.id != ?
        AND m.birth_date IS NOT NULL
        AND (
            (MONTH(m.birth_date) = MONTH(CURDATE()) AND DAY(m.birth_date) >= DAY(CURDATE()))
            OR 
            (MONTH(m.birth_date) = MONTH(DATE_ADD(CURDATE(), INTERVAL 1 MONTH)) 
             AND DAY(m.birth_date) <= DAY(DATE_ADD(CURDATE(), INTERVAL 30 DAY)))
        )
        ORDER BY 
            CASE 
                WHEN MONTH(m.birth_date) = MONTH(CURDATE()) THEN DAY(m.birth_date)
                ELSE DAY(m.birth_date) + 31
            END
    ");
    $stmt->execute([$userId]);
    $upcomingBirthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $upcomingBirthdays = [];
    $error = "Error loading birthdays: " . $e->getMessage();
}

// Get gift templates
try {
    $stmt = $pdo->prepare("
        SELECT id, template_name, template_category, template_content, template_style, preview_image_path
        FROM gift_templates 
        WHERE is_active = 1 AND template_type = 'birthday_card'
        ORDER BY template_category, template_name
    ");
    $stmt->execute();
    $giftTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $giftTemplates = [];
}

// Get payment settings
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM payment_settings WHERE setting_key IN ('birthday_gifts_enabled', 'min_gift_amount', 'max_gift_amount', 'default_gift_message')");
    $stmt->execute();
    $paymentSettings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $paymentSettings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    $paymentSettings = [
        'birthday_gifts_enabled' => '1',
        'min_gift_amount' => '5',
        'max_gift_amount' => '500',
        'default_gift_message' => 'Wishing you a wonderful birthday!'
    ];
}

// Get site settings for branding
$sitename = 'Organization Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Gifts - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: #f8f9fa;
            font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: var(--bs-primary, #fd7e14) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: white !important;
        }

        .gifts-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .gifts-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }

        .birthday-member-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .birthday-member-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .member-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--bs-primary, #fd7e14);
        }

        .member-avatar-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--bs-primary, #fd7e14), var(--bs-secondary, #6c757d));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .birthday-badge {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }

        .birthday-today {
            background: linear-gradient(135deg, #ff6b6b, #ff4757);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .gift-type-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 100%;
        }

        .gift-type-card:hover {
            border-color: var(--bs-primary, #fd7e14);
            background-color: rgba(253, 126, 20, 0.05);
            transform: translateY(-3px);
        }

        .gift-type-card.selected {
            border-color: var(--bs-primary, #fd7e14);
            background-color: rgba(253, 126, 20, 0.1);
        }

        .gift-type-icon {
            font-size: 3rem;
            color: var(--bs-primary, #fd7e14);
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: var(--bs-primary, #fd7e14);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(253, 126, 20, 0.3);
        }

        .btn-gift {
            background: linear-gradient(135deg, var(--bs-primary, #fd7e14), #ff6b6b);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-gift:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
            color: white;
        }

        .period-header {
            background: linear-gradient(135deg, var(--bs-primary, #fd7e14), var(--bs-secondary, #6c757d));
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <!-- Logo and brand -->
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-door"></i> <span><?php echo htmlspecialchars($sitename); ?></span>
            </a>

            <!-- Mobile toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Empty left side to push everything to the right -->
                <div class="navbar-nav me-auto"></div>

                <!-- Main navigation items on the right -->
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="prayer_requests.php">
                            <i class="bi bi-heart"></i> Prayer Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="family_management.php">
                            <i class="bi bi-people"></i> Family
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="volunteer_opportunities.php">
                            <i class="bi bi-person-workspace"></i> Volunteer
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="skills.php">
                            <i class="bi bi-tools"></i> Skills
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="birthday_gifts.php">
                            <i class="bi bi-gift"></i> Gifts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="birthday_templates.php">
                            <i class="bi bi-card-text"></i> Birthdays
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>

                <!-- User dropdown -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="family_management.php"><i class="bi bi-people"></i> Family Management</a></li>
                            <li><a class="dropdown-item" href="prayer_requests.php"><i class="bi bi-heart"></i> Prayer Requests</a></li>
                            <li><a class="dropdown-item" href="volunteer_opportunities.php"><i class="bi bi-person-workspace"></i> Volunteer Opportunities</a></li>
                            <li><a class="dropdown-item" href="skills.php"><i class="bi bi-tools"></i> My Skills</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container gifts-container">
        <!-- Messages -->
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="gifts-card">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h2><i class="bi bi-gift"></i> Birthday Gifts</h2>
                    <p class="text-muted mb-0">Send thoughtful gifts to celebrate birthdays in your community</p>
                </div>
                <div>
                    <a href="../donate.php" class="btn btn-outline-primary">
                        <i class="bi bi-heart"></i> Donate to Organization
                    </a>
                </div>
            </div>
        </div>
