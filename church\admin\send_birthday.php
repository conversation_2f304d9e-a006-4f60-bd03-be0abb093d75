<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$success_message = '';
$error_message = '';
$preview_mode = false;
$preview_content = '';
$preview_subject = '';
$member = null;
$templates = [];

// Check if member ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $error_message = "No member ID provided. Please select a member from the list.";
    header("Location: dashboard.php");
    exit();
}

$member_id = intval($_GET['id']);

// Get member details
try {
    $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$member_id]);
    $member = $stmt->fetch();
    
    if (!$member) {
        $error_message = "Member not found. The member may have been deleted.";
        header("Location: index.php");
        exit();
    }
} catch (PDOException $e) {
    $error_message = log_db_error("retrieving member details", $e, [$member_id]);
    header("Location: index.php");
    exit();
}

// Get birthday email templates
try {
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_name");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    // If no birthday templates exist, create a default one
    if (empty($templates)) {
        // Check if any templates exist that might be usable
        $stmt = $conn->prepare("SELECT * FROM email_templates WHERE template_name LIKE ? ORDER BY template_name LIMIT 1");
        $stmt->execute(['%birthday%']);
        $existing_template = $stmt->fetch();
        
        if ($existing_template) {
            // Mark this template as a birthday template
            $update_stmt = $conn->prepare("UPDATE email_templates SET is_birthday_template = 1 WHERE id = ?");
            $update_stmt->execute([$existing_template['id']]);
            
            // Add to templates array
            $existing_template['is_birthday_template'] = 1;
            $templates[] = $existing_template;
        } else {
            // Create a default birthday template
            $default_template = [
                'template_name' => 'Default Birthday Template',
                'subject' => 'Happy Birthday, {full_name}!',
                'content' => '
                <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; padding: 20px;">
                    <div style="text-align: center;">
                        <img src="{church_logo}" alt="Church Logo" style="max-width: 200px; margin-bottom: 20px;">
                    </div>
                    <h1 style="color: #3498db;">Happy Birthday, {full_name}!</h1>
                    <p>Dear {full_name},</p>
                    <p>On behalf of everyone at Freedom Assembly Church International, we want to wish you a very happy birthday!</p>
                    <p>May this special day bring you joy, happiness, and God\'s abundant blessings.</p>
                    <p>Birthdays are a reminder of God\'s grace and love in our lives. We are thankful for you and the role you play in our church family.</p>
                    <div style="background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px;">
                        <p style="font-style: italic; text-align: center;">"This is the day which the Lord has made; let us rejoice and be glad in it." — Psalm 118:24</p>
                    </div>
                    <p>Blessings,<br>Freedom Assembly Church International</p>
                    <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
                        <p>Freedom Assembly Church International<br>
                        <a href="{site_url}" style="color: #3498db; text-decoration: none;">Visit our website</a></p>
                    </div>
                </div>',
                'is_birthday_template' => 1,
                'template_category' => 'birthday'
            ];
            
            try {
                $insert_stmt = $conn->prepare("INSERT INTO email_templates 
                    (template_name, subject, content, is_birthday_template, template_category) 
                    VALUES (?, ?, ?, ?, ?)");
                
                $insert_stmt->execute([
                    $default_template['template_name'],
                    $default_template['subject'],
                    $default_template['content'],
                    $default_template['is_birthday_template'],
                    $default_template['template_category']
                ]);
                
                $default_template['id'] = $conn->lastInsertId();
                $templates[] = $default_template;
                
                $message = "A default birthday template has been created.";
            } catch (PDOException $e) {
                $error_message = log_db_error("creating default birthday template", $e);
            }
        }
    }
} catch (PDOException $e) {
    $error_message = log_db_error("retrieving email templates", $e);
}

// Handle template preview
if (isset($_GET['preview']) && isset($_POST['template_id'])) {
    $preview_mode = true;
    $template_id = intval($_POST['template_id']);
    
    try {
        $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();
        
        if ($template) {
            // Debug info for troubleshooting
            $debug_info = "<!-- Debug: Processing template preview -->\n";
            
            // Prepare member data for placeholders
            $memberData = [
                'full_name' => $member['full_name'],
                'email' => $member['email'],
                'phone_number' => $member['phone_number'] ?? '',
                'birth_date' => $member['birth_date'] ?? '',
                'image_path' => $member['image_path'] ?? '',
                'email_id' => uniqid('email_')  // Add a unique ID for tracking
            ];
            
            // Debug member data
            $debug_info .= "<!-- Debug: Member data: " . htmlspecialchars(json_encode($memberData)) . " -->\n";
            
            // Debug image path
            if (isset($member['image_path']) && !empty($member['image_path'])) {
                $debug_info .= "<!-- Debug: Member image path: " . htmlspecialchars($member['image_path']) . " -->\n";
                
                // Check if file exists on server
                $image_file_path = realpath(__DIR__ . '/../' . $member['image_path']);
                $debug_info .= "<!-- Debug: Full server path: " . htmlspecialchars($image_file_path) . " -->\n";
                $debug_info .= "<!-- Debug: File exists: " . (file_exists($image_file_path) ? 'Yes' : 'No') . " -->\n";
                
                // Check URL construction
                $image_url = SITE_URL . '/' . ltrim($member['image_path'], '/');
                $debug_info .= "<!-- Debug: Image URL: " . htmlspecialchars($image_url) . " -->\n";
            } else {
                $debug_info .= "<!-- Debug: No image path found for member -->\n";
            }
            
            // Extract first and last name from full name if needed
            if (!empty($member['full_name'])) {
                $nameParts = explode(' ', $member['full_name'], 2);
                $memberData['first_name'] = $nameParts[0];
                $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
                $debug_info .= "<!-- Debug: Extracted first_name: " . htmlspecialchars($memberData['first_name']) . " -->\n";
            }
            
            // Add recipient-specific placeholders for compatibility with bulk email templates
            $memberData['recipient_full_name'] = $memberData['full_name'];
            $memberData['recipient_first_name'] = $memberData['first_name'];
            $memberData['recipient_email'] = $memberData['email'];
            $memberData['recipient_phone'] = $memberData['phone_number'];
            
            // Add birthday-specific placeholders
            $memberData['birthday_date'] = date('F j', strtotime($memberData['birth_date'] ?? 'now'));
            $memberData['birthday_year'] = date('Y', strtotime($memberData['birth_date'] ?? 'now'));
            $memberData['current_year'] = date('Y');
            $memberData['current_date'] = date('F j, Y');
            $memberData['current_time'] = date('g:i A');
            
            // Calculate upcoming birthday date
            $birth_month = date('m', strtotime($memberData['birth_date'] ?? 'now'));
            $birth_day = date('d', strtotime($memberData['birth_date'] ?? 'now'));
            $birth_year = date('Y', strtotime($memberData['birth_date'] ?? 'now'));
            $current_year = date('Y');
            $next_year = $current_year + 1;
            
            // Determine if birthday has passed this year already
            $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
            $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
            
            // Use next year's date if this year's birthday has already passed
            $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;
            
            // Calculate age on upcoming birthday
            $age = strtotime($this_year_birthday) < time() ? 
                  ($next_year - $birth_year) : 
                  ($current_year - $birth_year);
            
            // Add days until birthday
            $days_until = ceil((strtotime($upcoming_date) - time()) / 86400);
            
            // Add more specific birthday placeholders
            $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
            $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
            $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
            $memberData['days_until_birthday'] = $days_until;
            $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                      ($days_until == 1 ? 'tomorrow' : 
                                      "in $days_until days");
            $memberData['age'] = $age;
            $memberData['birthday_member_age'] = $age;
            
            // Also add these placeholders to "birthday_member_" equivalents
            $memberData['birthday_member_birth_date'] = $memberData['birthday_date'];
            $memberData['birthday_member_name'] = $memberData['first_name'];
            $memberData['birthday_member_full_name'] = $memberData['full_name'];
            
            // Replace placeholders
            $preview_subject = replaceTemplatePlaceholders($template['subject'], $memberData);
            $preview_content = $debug_info . replaceTemplatePlaceholders($template['content'], $memberData);
            
            // Debug the final content
            $debug_info .= "<!-- Debug: Final subject: " . htmlspecialchars($preview_subject) . " -->\n";
        } else {
            $error_message = "Selected template not found.";
        }
    } catch (PDOException $e) {
        $error_message = log_db_error("generating template preview", $e, [$template_id]);
    }
}

// Handle send email
if (isset($_POST['send_email']) && isset($_POST['template_id'])) {
    $template_id = intval($_POST['template_id']);
    
    try {
        $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();
        
        if ($template) {
            // Prepare member data for placeholders
            $memberData = [
                'full_name' => $member['full_name'],
                'email' => $member['email'],
                'phone_number' => $member['phone_number'] ?? '',
                'birth_date' => $member['birth_date'] ?? '',
                'image_path' => $member['image_path'] ?? '',
                'email_id' => uniqid('email_')  // Add a unique ID for tracking
            ];
            
            // Extract first and last name from full name if needed
            if (!empty($member['full_name'])) {
                $nameParts = explode(' ', $member['full_name'], 2);
                $memberData['first_name'] = $nameParts[0];
                $memberData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
            }
            
            // Add recipient-specific placeholders for compatibility with bulk email templates
            $memberData['recipient_full_name'] = $memberData['full_name'];
            $memberData['recipient_first_name'] = $memberData['first_name'];
            $memberData['recipient_email'] = $memberData['email'];
            $memberData['recipient_phone'] = $memberData['phone_number'];
            
            // Add birthday-specific placeholders
            $memberData['birthday_date'] = date('F j', strtotime($memberData['birth_date'] ?? 'now'));
            $memberData['birthday_year'] = date('Y', strtotime($memberData['birth_date'] ?? 'now'));
            $memberData['current_year'] = date('Y');
            $memberData['current_date'] = date('F j, Y');
            $memberData['current_time'] = date('g:i A');
            
            // Calculate upcoming birthday date
            $birth_month = date('m', strtotime($memberData['birth_date'] ?? 'now'));
            $birth_day = date('d', strtotime($memberData['birth_date'] ?? 'now'));
            $birth_year = date('Y', strtotime($memberData['birth_date'] ?? 'now'));
            $current_year = date('Y');
            $next_year = $current_year + 1;
            
            // Determine if birthday has passed this year already
            $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
            $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
            
            // Use next year's date if this year's birthday has already passed
            $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;
            
            // Calculate age on upcoming birthday
            $age = strtotime($this_year_birthday) < time() ? 
                  ($next_year - $birth_year) : 
                  ($current_year - $birth_year);
            
            // Add days until birthday
            $days_until = ceil((strtotime($upcoming_date) - time()) / 86400);
            
            // Add more specific birthday placeholders
            $memberData['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
            $memberData['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
            $memberData['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
            $memberData['days_until_birthday'] = $days_until;
            $memberData['days_text'] = $days_until == 0 ? 'today' : 
                                      ($days_until == 1 ? 'tomorrow' : 
                                      "in $days_until days");
            $memberData['age'] = $age;
            $memberData['birthday_member_age'] = $age;
            
            // Also add these placeholders to "birthday_member_" equivalents
            $memberData['birthday_member_birth_date'] = $memberData['birthday_date'];
            $memberData['birthday_member_name'] = $memberData['first_name'];
            $memberData['birthday_member_full_name'] = $memberData['full_name'];
            
            // Replace placeholders
            $email_subject = replaceTemplatePlaceholders($template['subject'], $memberData);
            $email_content = replaceTemplatePlaceholders($template['content'], $memberData);
            
            // Send the email
            $result = sendEmail(
                $member['email'],
                $memberData['full_name'],
                $email_subject,
                $email_content,
                true,
                $memberData
            );
            
            // Log the email sending attempt
            try {
                $log_stmt = $conn->prepare("INSERT INTO email_logs 
                    (member_id, template_id, subject, status, sent_by, email_id) 
                    VALUES (?, ?, ?, ?, ?, ?)");
                
                $status = $result ? 'sent' : 'failed';
                $sent_by = $_SESSION['admin_id'];
                
                $log_stmt->execute([
                    $member['id'],
                    $template_id,
                    $email_subject,
                    $status,
                    $sent_by,
                    $memberData['email_id']
                ]);
            } catch (PDOException $e) {
                error_log("Failed to log email: " . $e->getMessage());
                // Continue even if logging fails
            }
            
            if ($result) {
                $success_message = "Birthday email sent successfully to " . $member['full_name'] . "!";
            } else {
                $error_message = "Failed to send email. " . ($last_email_error ? "Error: " . $last_email_error : "Please check the logs for details.");
            }
        } else {
            $error_message = "Selected template not found.";
        }
    } catch (PDOException $e) {
        $error_message = log_db_error("sending birthday email", $e, [$template_id, $member['id']]);
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
        error_log("Error sending birthday email: " . $e->getMessage());
    }
}

// Page title
$page_title = "Send Birthday Email to " . $member['full_name'];

// Include header
include 'includes/header.php';
?>

<style>
    .member-info-table {
        margin-bottom: 0;
    }
    .member-info-table th {
        width: 100px;
        background-color: #f8f9fa;
        vertical-align: middle;
    }
    .member-info-table td {
        vertical-align: middle;
    }
    .member-info-value {
        font-size: 1.1em;
        color: #2c3e50;
    }
    .member-info-label {
        color: #6c757d;
        font-weight: 500;
    }
    .member-card {
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .member-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 2px solid #e9ecef;
        padding: 15px 20px;
    }
    .member-card .card-body {
        padding: 20px;
    }
    .member-header-icon {
        color: #3498db;
        margin-right: 8px;
    }
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2>Send Birthday Email</h2>
            <p class="text-muted">Send a personalized birthday email to <?php echo htmlspecialchars($member['full_name']); ?></p>
            
            <!-- Instructions panel -->
            <div class="alert alert-info mb-4 instruction-panel">
                <div class="d-flex justify-content-between align-items-start">
                    <h5><i class="bi bi-info-circle-fill me-2"></i>Sending Birthday Emails</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <p class="mb-2">This page allows you to send a personalized birthday email to a church member.</p>
                <ul class="mb-0">
                    <li><strong>Select Template:</strong> Choose from available birthday email templates</li>
                    <li><strong>Preview:</strong> See how the email will look before sending</li>
                    <li><strong>Personalization:</strong> The email will automatically include the member's name and other details</li>
                </ul>
                <div class="mt-2">
                    <strong>Note:</strong> You can create and manage birthday email templates in the Email Templates section.
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card member-card mb-4">
                <div class="card-header">
                    <i class="bi bi-person-circle member-header-icon"></i>
                    Member Information
                </div>
                <div class="card-body">
                    <table class="table member-info-table">
                        <tr>
                            <th class="member-info-label">Name:</th>
                            <td class="member-info-value">
                                <?php if (!empty($member['image_path'])): ?>
                                    <img src="../<?php echo htmlspecialchars($member['image_path']); ?>" 
                                         alt="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                         class="rounded-circle me-2"
                                         style="width: 40px; height: 40px; object-fit: cover;">
                                <?php else: ?>
                                    <i class="bi bi-person-circle me-2 text-secondary" style="font-size: 1.5em;"></i>
                                <?php endif; ?>
                                <?php echo htmlspecialchars($member['full_name'] ?? 'Unknown Member'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th class="member-info-label">Email:</th>
                            <td class="member-info-value">
                                <i class="bi bi-envelope me-2 text-muted"></i>
                                <?php echo htmlspecialchars($member['email'] ?? ''); ?>
                            </td>
                        </tr>
                        <tr>
                            <th class="member-info-label">Phone:</th>
                            <td class="member-info-value">
                                <i class="bi bi-telephone me-2 text-muted"></i>
                                <?php echo htmlspecialchars($member['phone_number'] ?? 'N/A'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th class="member-info-label">Birthday:</th>
                            <td class="member-info-value">
                                <i class="bi bi-calendar-event me-2 text-muted"></i>
                                <?php 
                                    $birth_date = $member['birth_date'] ?? $member['date_of_birth'] ?? null;
                                    echo $birth_date ? date('F d, Y', strtotime($birth_date)) : 'N/A';
                                ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <?php if (!$preview_mode): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-envelope-fill me-1"></i>
                        Select Email Template
                    </div>
                    <div class="card-body">
                        <form method="post" action="?id=<?php echo $member_id; ?>&preview=1">
                            <div class="mb-3">
                                <label for="template_id" class="form-label">Select Birthday Template</label>
                                <select class="form-select" id="template_id" name="template_id" required data-bs-toggle="tooltip" data-bs-placement="top" title="Choose a birthday email template. The template will be personalized with the member's information.">
                                    <option value="">Choose a template...</option>
                                    <?php foreach ($templates as $template): ?>
                                        <option value="<?php echo $template['id']; ?>" <?php echo (isset($_POST['template_id']) && $_POST['template_id'] == $template['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($template['template_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Preview how the email will look before sending">
                                    <i class="bi bi-eye me-2"></i>Preview Email
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-envelope-fill me-1"></i>
                        Send Email
                    </div>
                    <div class="card-body">
                        <form method="post" action="?id=<?php echo $member_id; ?>">
                            <input type="hidden" name="template_id" value="<?php echo intval($_POST['template_id']); ?>">
                            <div class="alert alert-info">
                                You are about to send a birthday email to <strong><?php echo htmlspecialchars($member['full_name']); ?></strong> at <strong><?php echo htmlspecialchars($member['email'] ?? ''); ?></strong>.
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" name="send_email" class="btn btn-success btn-lg">
                                    <i class="bi bi-envelope-fill me-1"></i> Send Birthday Email
                                </button>
                                <a href="?id=<?php echo $member_id; ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-1"></i> Back to Template Selection
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if ($preview_mode): ?>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-eye-fill me-1"></i>
                        Email Preview
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h5>Subject:</h5>
                            <div class="p-2 border rounded bg-light">
                                <?php echo htmlspecialchars($preview_subject ?? ''); ?>
                            </div>
                        </div>
                        <div>
                            <h5>Content:</h5>
                            <div class="border rounded overflow-auto" style="max-height: 500px;">
                                <div class="p-2">
                                    <?php echo $preview_content ?? ''; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 