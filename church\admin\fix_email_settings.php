<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

echo "<h1>Email Settings Fix & Test</h1>";

$message = '';
$error = '';

// Fix email settings synchronization
if (isset($_POST['fix_settings'])) {
    try {
        echo "<h2>Fixing Email Settings...</h2>";
        
        // Get current settings from settings table
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email')");
        $stmt->execute();
        $currentSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($currentSettings)) {
            echo "<p style='color: red;'>No email settings found in settings table. Please configure them first in <a href='settings.php'>Settings</a>.</p>";
        } else {
            echo "<p>Found " . count($currentSettings) . " email settings in settings table.</p>";
            
            // Create email_settings table if it doesn't exist
            $pdo->exec("CREATE TABLE IF NOT EXISTS email_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(50) NOT NULL UNIQUE,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
            
            echo "<p>✓ Email_settings table created/verified.</p>";
            
            // Map and sync settings
            $emailSettingsData = [];
            foreach ($currentSettings as $setting) {
                $key = $setting['setting_key'];
                $value = $setting['setting_value'];
                
                switch ($key) {
                    case 'smtp_encryption':
                        $emailSettingsData['smtp_secure'] = $value;
                        break;
                    case 'from_email':
                        $emailSettingsData['sender_email'] = $value;
                        break;
                    case 'from_name':
                        $emailSettingsData['sender_name'] = $value;
                        break;
                    case 'reply_to_email':
                        $emailSettingsData['reply_to_email'] = $value;
                        $emailSettingsData['replyToEmail'] = $value;
                        break;
                    default:
                        $emailSettingsData[$key] = $value;
                        break;
                }
            }
            
            // Add default smtp_auth
            $emailSettingsData['smtp_auth'] = '1';
            
            // Update email_settings table
            $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value, updated_at) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = VALUES(updated_at)");
            
            $successCount = 0;
            foreach ($emailSettingsData as $key => $value) {
                $result = $stmt->execute([$key, $value]);
                if ($result) {
                    echo "<p>✓ Updated: $key</p>";
                    $successCount++;
                } else {
                    echo "<p style='color: red;'>✗ Failed: $key</p>";
                }
            }
            
            echo "<p style='color: green;'><strong>Successfully synchronized $successCount email settings!</strong></p>";
            $message = "Email settings have been synchronized successfully.";
        }
        
    } catch (Exception $e) {
        $error = "Error fixing email settings: " . $e->getMessage();
        echo "<p style='color: red;'>$error</p>";
    }
}

// Test forgot password email
if (isset($_POST['test_forgot_password'])) {
    $test_username = $_POST['test_username'];
    
    try {
        echo "<h2>Testing Forgot Password for: $test_username</h2>";
        
        // Check if username exists
        $stmt = $pdo->prepare("SELECT id, email, full_name FROM admins WHERE username = ?");
        $stmt->execute([$test_username]);
        $admin = $stmt->fetch();
        
        if (!$admin) {
            echo "<p style='color: red;'>Admin user '$test_username' not found.</p>";
        } else {
            echo "<p>✓ Admin found: " . htmlspecialchars($admin['full_name']) . " (" . htmlspecialchars($admin['email']) . ")</p>";
            
            // Generate test token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+4 hours'));
            
            // Save token to database
            $stmt = $pdo->prepare("UPDATE admins SET password_reset_token = ?, password_reset_expires = ? WHERE id = ?");
            $stmt->execute([$token, $expires, $admin['id']]);
            
            echo "<p>✓ Reset token generated and saved.</p>";
            
            // Build reset URL
            $resetUrl = ADMIN_URL . "/reset_password.php?token=" . urlencode($token);
            echo "<p>✓ Reset URL: " . htmlspecialchars($resetUrl) . "</p>";
            
            // Test email sending
            echo "<p>Attempting to send email...</p>";
            
            // Create email content
            $subject = "Password Reset - " . get_organization_name() . " Admin";
            $content = "
                <html>
                <body>
                    <h2>Password Reset Request</h2>
                    <p>Dear " . htmlspecialchars($admin['full_name']) . ",</p>
                    <p>We received a request to reset your password for the " . htmlspecialchars(get_organization_name()) . " Admin Portal.</p>
                    <p>To reset your password, please click the link below:</p>
                    <p><a href='" . htmlspecialchars($resetUrl) . "'>Reset Password</a></p>
                    <p>This link will expire in 4 hours.</p>
                    <p>If you did not request a password reset, please ignore this email or contact an administrator.</p>
                    <p>Thank you,<br>" . htmlspecialchars(get_organization_name()) . " Admin Team</p>
                </body>
                </html>
            ";
            
            // Send email
            $emailSent = sendEmail(
                $admin['email'],
                $admin['full_name'],
                $subject,
                $content,
                true
            );
            
            if ($emailSent) {
                echo "<p style='color: green; font-weight: bold;'>✓ Password reset email sent successfully!</p>";
                $message = "Test password reset email sent successfully to " . $admin['email'];
            } else {
                echo "<p style='color: red; font-weight: bold;'>✗ Failed to send password reset email!</p>";
                
                global $last_email_error;
                if ($last_email_error) {
                    echo "<p><strong>Error:</strong> " . htmlspecialchars($last_email_error) . "</p>";
                    $error = "Email sending failed: " . $last_email_error;
                } else {
                    $error = "Email sending failed for unknown reason.";
                }
            }
        }
        
    } catch (Exception $e) {
        $error = "Error testing forgot password: " . $e->getMessage();
        echo "<p style='color: red;'>$error</p>";
    }
}

// Display current email settings
echo "<h2>Current Email Settings Status</h2>";

// Check settings table
echo "<h3>Settings Table:</h3>";
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '%smtp%' OR setting_key LIKE '%email%' OR setting_key LIKE '%from_%' ORDER BY setting_key");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($settings)) {
        echo "<p style='color: red;'>No email settings found in settings table.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Key</th><th>Value</th></tr>";
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            if (strpos($setting['setting_key'], 'password') !== false) {
                $value = str_repeat('*', strlen($value));
            }
            echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Check email_settings table
echo "<h3>Email_Settings Table:</h3>";
try {
    $stmt = $pdo->prepare("SELECT setting_key, setting_value, updated_at FROM email_settings ORDER BY setting_key");
    $stmt->execute();
    $emailSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($emailSettings)) {
        echo "<p style='color: orange;'>No settings found in email_settings table.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Key</th><th>Value</th><th>Updated</th></tr>";
        foreach ($emailSettings as $setting) {
            $value = $setting['setting_value'];
            if (strpos($setting['setting_key'], 'password') !== false) {
                $value = str_repeat('*', strlen($value));
            }
            echo "<tr><td>" . htmlspecialchars($setting['setting_key']) . "</td><td>" . htmlspecialchars($value) . "</td><td>" . htmlspecialchars($setting['updated_at']) . "</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

?>

<?php if ($message): ?>
    <div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;">
        <?php echo htmlspecialchars($message); ?>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<h2>Actions</h2>

<form method="POST" style="margin: 20px 0;">
    <h3>1. Fix Email Settings Synchronization</h3>
    <p>This will synchronize email settings from the settings table to the email_settings table.</p>
    <input type="submit" name="fix_settings" value="Fix Email Settings" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px;">
</form>

<form method="POST" style="margin: 20px 0;">
    <h3>2. Test Forgot Password Email</h3>
    <p>
        <label>Admin Username:</label><br>
        <input type="text" name="test_username" value="<?php echo htmlspecialchars($_POST['test_username'] ?? ''); ?>" required style="width: 200px; padding: 5px;">
    </p>
    <input type="submit" name="test_forgot_password" value="Test Forgot Password Email" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
</form>

<h2>Quick Links</h2>
<p>
    <a href="forgot_password.php">← Back to Forgot Password</a> |
    <a href="settings.php">Settings</a> |
    <a href="email_settings.php">Email Settings</a> |
    <a href="test_forgot_password_email.php">Detailed Email Test</a>
</p>
