<?php
/**
 * User Navigation Bar
 * 
 * Centralized navigation for user interface with organized submenus
 */

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);

// Helper function to check if current page is active
function isActivePage($page) {
    global $current_page;
    return $current_page === $page ? 'active' : '';
}

// Helper function to check if any page in array is active (for dropdown highlighting)
function isActiveSection($pages) {
    global $current_page;
    return in_array($current_page, $pages) ? 'active' : '';
}
?>

<nav class="navbar navbar-expand-lg">
    <div class="container">
        <a class="navbar-brand" href="dashboard.php">
            <?php
            // Use the existing logo management system
            $headerLogo = get_site_setting('header_logo', '');
            $mainLogo = get_site_setting('main_logo', '');
            $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

            if (!empty($logoToUse)): ?>
                <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                     alt="<?php echo get_organization_name(); ?>"
                     class="navbar-logo me-2">
                <?php echo htmlspecialchars(get_organization_name()); ?>
            <?php else: ?>
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename ?? 'Church Management'); ?>
            <?php endif; ?>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Empty left side to push everything to the right -->
            <div class="navbar-nav me-auto"></div>

            <!-- Main navigation items on the right -->
            <ul class="navbar-nav me-3">
                <li class="nav-item">
                    <a class="nav-link <?php echo isActivePage('dashboard.php'); ?>" href="dashboard.php">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>

                <!-- Events Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo isActiveSection(['events.php', 'birthday_templates.php', 'send_gift.php']); ?>"
                       href="#" id="eventsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-calendar-event"></i> Events
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="eventsDropdown">
                        <li><a class="dropdown-item <?php echo isActivePage('events.php'); ?>" href="events.php">
                            <i class="bi bi-calendar-event"></i> All Events
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('birthday_templates.php'); ?>" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item <?php echo isActivePage('send_gift.php'); ?>" href="send_gift.php">
                            <i class="bi bi-send"></i> Send Gift
                        </a></li>
                    </ul>
                </li>

                <!-- Volunteer Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo isActiveSection(['volunteer_opportunities.php', 'skills.php', 'family_management.php', 'enhanced_donate.php']); ?>"
                       href="#" id="volunteerDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-workspace"></i> Volunteer
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="volunteerDropdown">
                        <li><a class="dropdown-item <?php echo isActivePage('volunteer_opportunities.php'); ?>" href="volunteer_opportunities.php">
                            <i class="bi bi-person-workspace"></i> Opportunities
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('skills.php'); ?>" href="skills.php">
                            <i class="bi bi-tools"></i> Skills
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('family_management.php'); ?>" href="family_management.php">
                            <i class="bi bi-people"></i> Family
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item <?php echo isActivePage('enhanced_donate.php'); ?>" href="enhanced_donate.php">
                            <i class="bi bi-heart"></i> Donation
                        </a></li>
                    </ul>
                </li>

                <!-- Requests -->
                <li class="nav-item">
                    <a class="nav-link <?php echo isActivePage('requests.php'); ?>" href="requests.php">
                        <i class="bi bi-chat-heart"></i> Requests
                    </a>
                </li>

                <!-- My Gifts -->
                <li class="nav-item">
                    <a class="nav-link <?php echo isActivePage('my_gifts.php'); ?>" href="my_gifts.php">
                        <i class="bi bi-gift"></i> My Gifts
                    </a>
                </li>
            </ul>

            <!-- User dropdown -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle"></i> 
                        <?php 
                        if (isset($userData) && !empty($userData)) {
                            echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']);
                        } else {
                            echo 'User';
                        }
                        ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="bi bi-person"></i> My Profile
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a></li>
                        <li><a class="dropdown-item" href="family_management.php">
                            <i class="bi bi-people"></i> Family Management
                        </a></li>
                        <li><a class="dropdown-item" href="change_password.php">
                            <i class="bi bi-shield-lock"></i> Change Password
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
/* Custom styles for the navbar */
.navbar-nav .dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.navbar-nav .dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.15s ease-in-out;
}

.navbar-nav .dropdown-item:hover {
    background-color: #f8f9fa;
}

.navbar-nav .dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.navbar-nav .dropdown-toggle.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* Mobile responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-nav .dropdown-menu {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.95);
    }
    
    .navbar-nav .dropdown-item {
        color: #333;
    }
}
</style>
