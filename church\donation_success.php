<?php
/**
 * Enhanced Donation Success Page
 *
 * Shows confirmation after successful donation/gift processing with enhanced features
 */

require_once 'config.php';

// Get donation ID
$donation_id = filter_var($_GET['id'] ?? null, FILTER_VALIDATE_INT);
if (!$donation_id) {
    header("Location: donate.php");
    exit();
}

// Get donation details with enhanced information
try {
    $stmt = $pdo->prepare("
        SELECT d.*,
               m.full_name as recipient_name,
               m.email as recipient_email,
               gt.template_name
        FROM donations d
        LEFT JOIN members m ON d.recipient_id = m.id
        LEFT JOIN gift_templates gt ON d.gift_template_id = gt.id
        WHERE d.id = ? AND d.payment_status IN ('completed', 'pending')
    ");
    $stmt->execute([$donation_id]);
    $donation = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$donation) {
        header("Location: donate.php");
        exit();
    }

    // Get organization information
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type')");
    $stmt->execute();
    $org_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $org_settings[$row['setting_key']] = $row['setting_value'];
    }

    $organization_name = $org_settings['organization_name'] ?? 'Church Management System';
    $organization_type = $org_settings['organization_type'] ?? 'church';

    // Get payment settings for success message
    $stmt = $pdo->prepare("SELECT * FROM payment_settings");
    $stmt->execute();
    $payment_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $payment_settings[$row['setting_key']] = $row['setting_value'];
    }

} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    header("Location: donate.php");
    exit();
}

// Currency options
$currencies = [
    'USD' => ['name' => 'US Dollar', 'symbol' => '$'],
    'EUR' => ['name' => 'Euro', 'symbol' => '€'],
    'GBP' => ['name' => 'British Pound', 'symbol' => '£'],
    'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R'],
    'NGN' => ['name' => 'Nigerian Naira', 'symbol' => '₦'],
    'KES' => ['name' => 'Kenyan Shilling', 'symbol' => 'KSh'],
    'UGX' => ['name' => 'Ugandan Shilling', 'symbol' => 'USh'],
    'GHS' => ['name' => 'Ghanaian Cedi', 'symbol' => 'GH₵']
];

$currency_symbol = $currencies[$donation['currency']]['symbol'] ?? '';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donation Successful - Freedom Assembly Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .success-icon {
            font-size: 4rem;
            color: #198754;
        }
        .donation-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <?php include 'navbar.php'; ?>
    
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <i class="bi bi-check-circle-fill success-icon mb-4"></i>
                <h1 class="mb-4">Thank You for Your Donation!</h1>
                
                <?php if (isset($payment_settings['donation_success_message'])): ?>
                    <p class="lead mb-5"><?php echo htmlspecialchars($payment_settings['donation_success_message']); ?></p>
                <?php endif; ?>
                
                <div class="donation-details text-start mb-5">
                    <h4 class="mb-4">
                        <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                            🎉 Birthday Gift Details
                        <?php else: ?>
                            Donation Details
                        <?php endif; ?>
                    </h4>
                    <div class="row">
                        <div class="col-sm-6">
                            <p><strong>Transaction ID:</strong><br>
                            #<?php echo $donation['id']; ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Amount:</strong><br>
                            <span class="h5 text-success"><?php echo $currency_symbol . number_format($donation['amount'], 2); ?></span></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Date:</strong><br>
                            <?php echo date('F j, Y \a\t g:i A', strtotime($donation['created_at'])); ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Type:</strong><br>
                            <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-gift"></i> Birthday Gift
                                </span>
                            <?php else: ?>
                                <span class="badge bg-primary">
                                    <i class="bi bi-heart"></i> General Donation
                                </span>
                            <?php endif; ?>
                            </p>
                        </div>

                        <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                            <div class="col-sm-6">
                                <p><strong>Recipient:</strong><br>
                                <?php echo htmlspecialchars($donation['recipient_name']); ?>
                                <?php if ($donation['anonymous_gift']): ?>
                                    <span class="badge bg-warning text-dark ms-2">
                                        <i class="bi bi-eye-slash"></i> Anonymous
                                    </span>
                                <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-sm-6">
                                <p><strong>Gift Type:</strong><br>
                                <?php
                                $gift_icons = [
                                    'monetary' => 'bi-cash-coin text-success',
                                    'digital_card' => 'bi-card-image text-primary',
                                    'digital_message' => 'bi-chat-heart text-info',
                                    'gift_card' => 'bi-gift text-warning'
                                ];
                                $icon = $gift_icons[$donation['gift_type']] ?? 'bi-question-circle';
                                ?>
                                <i class="bi <?php echo $icon; ?>"></i>
                                <?php echo ucfirst(str_replace('_', ' ', $donation['gift_type'])); ?>
                                <?php if ($donation['template_name']): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($donation['template_name']); ?></small>
                                <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-sm-6">
                                <p><strong>Delivery:</strong><br>
                                <?php if ($donation['delivery_method'] === 'immediate'): ?>
                                    <span class="badge bg-success">
                                        <i class="bi bi-lightning-fill"></i> Immediate
                                    </span>
                                <?php elseif ($donation['delivery_method'] === 'scheduled'): ?>
                                    <span class="badge bg-info">
                                        <i class="bi bi-calendar-event"></i> Scheduled
                                    </span>
                                    <?php if ($donation['scheduled_delivery_date']): ?>
                                        <br><small class="text-muted">
                                            For <?php echo date('F j, Y', strtotime($donation['scheduled_delivery_date'])); ?>
                                        </small>
                                    <?php endif; ?>
                                <?php endif; ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <?php if ($donation['sender_organization']): ?>
                            <div class="col-sm-6">
                                <p><strong>Organization:</strong><br>
                                <i class="bi bi-building text-info"></i>
                                <?php echo htmlspecialchars($donation['sender_organization']); ?>
                                <?php if ($donation['sender_department']): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($donation['sender_department']); ?></small>
                                <?php endif; ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <div class="col-sm-6">
                            <p><strong>Payment Method:</strong><br>
                            <?php echo ucfirst($donation['payment_method']); ?></p>
                        </div>

                        <?php if ($donation['message']): ?>
                        <div class="col-12">
                            <p><strong>Message:</strong><br>
                            <?php echo nl2br(htmlspecialchars($donation['message'])); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <div>
                                <strong>Gift Notification:</strong>
                                <?php if ($donation['delivery_method'] === 'immediate'): ?>
                                    Your birthday gift notification has been sent to <?php echo htmlspecialchars($donation['recipient_name']); ?>!
                                <?php else: ?>
                                    Your birthday gift will be delivered on the scheduled date.
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <p>A confirmation email has been sent to <?php echo htmlspecialchars($donation['donor_email']); ?></p>

                <div class="mt-4">
                    <a href="register.php" class="btn btn-primary me-3">Return to Homepage</a>
                    <a href="donate.php" class="btn btn-outline-primary">Make Another Donation</a>
                </div>
            </div>
        </div>
    </div>
    
    <?php include 'footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 